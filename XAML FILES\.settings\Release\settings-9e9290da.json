﻿{
  "UiPath.System.Activities.AddDataColumn.AllowDBNull": "True",
  "UiPath.System.Activities.AddDataColumn.AutoIncrement": "False",
  "UiPath.System.Activities.AddDataColumn.MaxLength": "100",
  "UiPath.System.Activities.AddDataColumn.Unique": "False",
  "UiPath.System.Activities.ReadTextFile.Encoding": "",
  "UiPath.System.Activities.WriteTextFile.Encoding": "",
  "UiPath.System.Activities.AppendLine.Encoding": "",
  "UiPath.System.Activities.FilterDataTable.FilterRowsMode": "Keep",
  "UiPath.System.Activities.InvokeWorkflowFile.Timeout": "0",
  "UiPath.System.Activities.InvokeWorkflowFile.LogEntry": "No",
  "UiPath.System.Activities.InvokeWorkflowFile.LogExit": "No",
  "UiPath.System.Activities.LogMessage.Level": "Info",
  "UiPath.System.Activities.MessageBox.Buttons": "Ok",
  "UiPath.System.Activities.MessageBox.TopMost": "True",
  "UiPath.System.Activities.OrchestratorHTTPRequest.RelativeEndpoint": "",
  "UiPath.System.Activities.RetryScope.NumberOfRetries": "3",
  "UiPath.System.Activities.RetryScope.RetryInterval": "5000"
}