<Activity mc:Ignorable="sap sap2010" x:Class="FilterCustomerNames" xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities" xmlns:av="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:d="clr-namespace:DTtoHTML;assembly=DTtoHTML" xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" xmlns:mg="clr-namespace:Microsoft.Graph;assembly=Microsoft.Graph" xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities" xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation" xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation" xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib" xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib" xmlns:sd="clr-namespace:System.Data;assembly=System.Data" xmlns:ui="http://schemas.uipath.com/workflow/activities" xmlns:uma="clr-namespace:UiPath.MicrosoftOffice365.Activities;assembly=UiPath.MicrosoftOffice365.Activities" xmlns:umam="clr-namespace:UiPath.MicrosoftOffice365.Activities.Mail;assembly=UiPath.MicrosoftOffice365.Activities" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="in_Config" Type="InArgument(scg:Dictionary(x:String, x:Object))" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap:VirtualizedContainerService.HintSize>624,797.333333333333</sap:VirtualizedContainerService.HintSize>
  <sap2010:WorkflowViewState.IdRef>FilterCustomerNames_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <scg:List x:TypeArguments="x:String" Capacity="44">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Microsoft.VisualBasic</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>System</x:String>
      <x:String>System.Collections</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Data</x:String>
      <x:String>System.Diagnostics</x:String>
      <x:String>System.Drawing</x:String>
      <x:String>System.IO</x:String>
      <x:String>System.Linq</x:String>
      <x:String>System.Net.Mail</x:String>
      <x:String>System.Xml</x:String>
      <x:String>System.Xml.Linq</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>UiPath.Core</x:String>
      <x:String>UiPath.Core.Activities</x:String>
      <x:String>System.ComponentModel</x:String>
      <x:String>System.Runtime.Serialization</x:String>
      <x:String>System.Xml.Serialization</x:String>
      <x:String>System.Reflection</x:String>
      <x:String>System.Runtime.InteropServices</x:String>
      <x:String>UiPath.Excel</x:String>
      <x:String>UiPath.Excel.Activities</x:String>
      <x:String>BalaReva.Excel.Sheets</x:String>
      <x:String>BalaReva.Excel.Base</x:String>
      <x:String>DTtoHTML</x:String>
      <x:String>System.Text</x:String>
      <x:String>System.Collections.ObjectModel</x:String>
      <x:String>System.Activities.DynamicUpdate</x:String>
      <x:String>System.Web</x:String>
      <x:String>Microsoft.Graph</x:String>
      <x:String>System.Security</x:String>
      <x:String>UiPath.MicrosoftOffice365.Enums</x:String>
      <x:String>UiPath.MicrosoftOffice365.Activities</x:String>
      <x:String>UiPath.Shared.Activities</x:String>
      <x:String>UiPath.MicrosoftOffice365.Activities.Mail</x:String>
      <x:String>UiPath.DataTableUtilities</x:String>
      <x:String>System.Text.RegularExpressions</x:String>
      <x:String>UiPath.MicrosoftOffice365.Activities.Excel</x:String>
    </scg:List>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>BalaReva.Excel</AssemblyReference>
      <AssemblyReference>BalaReva.Excel.Base</AssemblyReference>
      <AssemblyReference>DTtoHTML</AssemblyReference>
      <AssemblyReference>DTtoHTML_Expressions_52584287</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>Microsoft.Graph</AssemblyReference>
      <AssemblyReference>Microsoft.Graph.Core</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>System.ComponentModel.TypeConverter</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>System.Data</AssemblyReference>
      <AssemblyReference>System.Data.DataSetExtensions</AssemblyReference>
      <AssemblyReference>System.Data.Entity</AssemblyReference>
      <AssemblyReference>System.Drawing</AssemblyReference>
      <AssemblyReference>System.Linq</AssemblyReference>
      <AssemblyReference>System.Linq.Async</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
      <AssemblyReference>System.ObjectModel</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System.Text.Encoding.CodePages</AssemblyReference>
      <AssemblyReference>System.ValueTuple</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>System.Xml</AssemblyReference>
      <AssemblyReference>System.Xml.Linq</AssemblyReference>
      <AssemblyReference>UiPath.Excel</AssemblyReference>
      <AssemblyReference>UiPath.Excel.Activities</AssemblyReference>
      <AssemblyReference>UiPath.Excel.Activities.Design</AssemblyReference>
      <AssemblyReference>UiPath.MicrosoftOffice365</AssemblyReference>
      <AssemblyReference>UiPath.MicrosoftOffice365.Activities</AssemblyReference>
      <AssemblyReference>UiPath.MicrosoftOffice365.Activities.Design</AssemblyReference>
      <AssemblyReference>UiPath.OCR.Activities</AssemblyReference>
      <AssemblyReference>UiPath.Studio.Constants</AssemblyReference>
      <AssemblyReference>UiPath.System.Activities</AssemblyReference>
      <AssemblyReference>UiPath.System.Activities.Design</AssemblyReference>
      <AssemblyReference>UiPath.Testing.Activities</AssemblyReference>
      <AssemblyReference>UiPath.UiAutomation.Activities</AssemblyReference>
      <AssemblyReference>UiPath.UIAutomationCore</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Flowchart sap2010:Annotation.AnnotationText="1. Read range the formatted Transport Misses file&#xA;2. Select each customer names dat and copy into a different sheet&#xA;3. for particular customer data remove duplicates from column L to N and C to K&#xA;4. to select email id, chekc if ID = 1 or X&#xA;5. email the data from c to k for each customers" DisplayName="FilterCustomerNames" sap:VirtualizedContainerService.HintSize="634,732.************" sap2010:WorkflowViewState.IdRef="Flowchart_1">
    <Flowchart.Variables>
      <Variable x:TypeArguments="sd:DataTable" Name="dt_Complete" />
      <Variable x:TypeArguments="sd:DataTable" Name="dt_Customer" />
      <Variable x:TypeArguments="sd:DataTable" Name="dt_uniqueCustomers" />
      <Variable x:TypeArguments="sd:DataTable" Name="dt_Initial" />
      <Variable x:TypeArguments="sd:DataTable" Name="dt_FilteredEmailDataOnly" />
      <Variable x:TypeArguments="sd:DataTable" Name="dt_emailDataOnly" />
      <Variable x:TypeArguments="sd:DataTable" Name="dt_emailBodyData" />
      <Variable x:TypeArguments="sd:DataTable" Name="dt_emailBodyCorrectFormat" />
      <Variable x:TypeArguments="sd:DataTable" Name="dt_AtoK" />
      <Variable x:TypeArguments="sd:DataTable" Name="dt_emailBodyCorrectFormatWithoutSpace" />
      <Variable x:TypeArguments="x:Int32" Default="0" Name="rowIndexEmail" />
      <Variable x:TypeArguments="x:String" Name="strEmailRecipients" />
      <Variable x:TypeArguments="x:String" Name="strCustomerName" />
      <Variable x:TypeArguments="sd:DataTable" Name="dt_FilteredEmailDataWhole" />
    </Flowchart.Variables>
    <sap:WorkflowViewStateService.ViewState>
      <scg:Dictionary x:TypeArguments="x:String, x:Object">
        <x:Boolean x:Key="IsExpanded">True</x:Boolean>
        <av:Point x:Key="ShapeLocation">270,2.5</av:Point>
        <av:Size x:Key="ShapeSize">60,74.6************</av:Size>
        <av:PointCollection x:Key="ConnectorLocation">300,77.1************ 300,90 350,90</av:PointCollection>
        <x:Boolean x:Key="IsAnnotationDocked">True</x:Boolean>
      </scg:Dictionary>
    </sap:WorkflowViewStateService.ViewState>
    <Flowchart.StartNode>
      <x:Reference>__ReferenceID5</x:Reference>
    </Flowchart.StartNode>
    <FlowStep x:Name="__ReferenceID3">
      <sap:WorkflowViewStateService.ViewState>
        <scg:Dictionary x:TypeArguments="x:String, x:Object">
          <av:Point x:Key="ShapeLocation">200,124.02</av:Point>
          <av:Size x:Key="ShapeSize">200,54</av:Size>
          <av:PointCollection x:Key="ConnectorLocation">300,178.02 300,194.02</av:PointCollection>
        </scg:Dictionary>
      </sap:WorkflowViewStateService.ViewState>
      <ui:ReadRange AddHeaders="True" DataTable="[dt_Initial]" DisplayName="Read Range Full RPA renamed sheet required data" sap:VirtualizedContainerService.HintSize="200,54" sap2010:WorkflowViewState.IdRef="ReadRange_1" Range="[&quot;A1:N&quot;+(dt_Complete.rows.count+1).ToString]" SheetName="RPARenamed" WorkbookPath="[in_Config(&quot;TransportMissesInputFilesPath&quot;).ToString+&quot;\&quot;+date.today.tostring(&quot;MMMM yyyy&quot;)+&quot;\&quot;+date.Today.ToString(&quot;dd.MM.yyyy&quot;)+&quot;\&quot;+&quot;Transport misses &quot;+date.Today.ToString(&quot;dd.MM.yyyy&quot;) +&quot;.XLSX&quot;]">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </ui:ReadRange>
      <FlowStep.Next>
        <FlowStep x:Name="__ReferenceID0">
          <sap:WorkflowViewStateService.ViewState>
            <scg:Dictionary x:TypeArguments="x:String, x:Object">
              <av:Point x:Key="ShapeLocation">200,194.02</av:Point>
              <av:Size x:Key="ShapeSize">200,54</av:Size>
              <av:PointCollection x:Key="ConnectorLocation">300,248.02 300,264.02</av:PointCollection>
            </scg:Dictionary>
          </sap:WorkflowViewStateService.ViewState>
          <ui:ReadRange AddHeaders="True" DataTable="[dt_Customer]" DisplayName="Read Range customer names range" sap:VirtualizedContainerService.HintSize="200,54" sap2010:WorkflowViewState.IdRef="ReadRange_2" Range="[&quot;A1:A&quot;+(dt_Initial.rows.count+1).ToString]" SheetName="RPARenamed" WorkbookPath="[in_Config(&quot;TransportMissesInputFilesPath&quot;).ToString+&quot;\&quot;+date.today.tostring(&quot;MMMM yyyy&quot;)+&quot;\&quot;+date.Today.ToString(&quot;dd.MM.yyyy&quot;)+&quot;\&quot;+&quot;Transport misses &quot;+date.Today.ToString(&quot;dd.MM.yyyy&quot;) +&quot;.XLSX&quot;]">
            <sap:WorkflowViewStateService.ViewState>
              <scg:Dictionary x:TypeArguments="x:String, x:Object">
                <x:Boolean x:Key="IsExpanded">True</x:Boolean>
              </scg:Dictionary>
            </sap:WorkflowViewStateService.ViewState>
          </ui:ReadRange>
          <FlowStep.Next>
            <FlowStep x:Name="__ReferenceID1">
              <sap:WorkflowViewStateService.ViewState>
                <scg:Dictionary x:TypeArguments="x:String, x:Object">
                  <av:Point x:Key="ShapeLocation">200,264.02</av:Point>
                  <av:Size x:Key="ShapeSize">200,54</av:Size>
                  <av:PointCollection x:Key="ConnectorLocation">300,318.02 300,334.02</av:PointCollection>
                </scg:Dictionary>
              </sap:WorkflowViewStateService.ViewState>
              <ui:RemoveDuplicateRows DataTable="[dt_Customer]" DisplayName="Remove Duplicate Rows customer names" sap:VirtualizedContainerService.HintSize="200,54" sap2010:WorkflowViewState.IdRef="RemoveDuplicateRows_1" OutputDataTable="[dt_uniqueCustomers]">
                <sap:WorkflowViewStateService.ViewState>
                  <scg:Dictionary x:TypeArguments="x:String, x:Object">
                    <x:Boolean x:Key="IsExpanded">True</x:Boolean>
                  </scg:Dictionary>
                </sap:WorkflowViewStateService.ViewState>
              </ui:RemoveDuplicateRows>
              <FlowStep.Next>
                <FlowStep x:Name="__ReferenceID4">
                  <sap:WorkflowViewStateService.ViewState>
                    <scg:Dictionary x:TypeArguments="x:String, x:Object">
                      <av:Point x:Key="ShapeLocation">200,334.02</av:Point>
                      <av:Size x:Key="ShapeSize">200,54</av:Size>
                      <av:PointCollection x:Key="ConnectorLocation">300,388.02 300,404.02</av:PointCollection>
                    </scg:Dictionary>
                  </sap:WorkflowViewStateService.ViewState>
                  <ui:LogMessage DisplayName="Log Message- for each customers" sap:VirtualizedContainerService.HintSize="200,54" sap2010:WorkflowViewState.IdRef="LogMessage_1" Level="Info" Message="[&quot;Filtering customer details for each customers started&quot;]">
                    <sap:WorkflowViewStateService.ViewState>
                      <scg:Dictionary x:TypeArguments="x:String, x:Object">
                        <x:Boolean x:Key="IsExpanded">True</x:Boolean>
                      </scg:Dictionary>
                    </sap:WorkflowViewStateService.ViewState>
                  </ui:LogMessage>
                  <FlowStep.Next>
                    <FlowStep x:Name="__ReferenceID2">
                      <sap:WorkflowViewStateService.ViewState>
                        <scg:Dictionary x:TypeArguments="x:String, x:Object">
                          <av:Point x:Key="ShapeLocation">200,404.02</av:Point>
                          <av:Size x:Key="ShapeSize">200,54</av:Size>
                        </scg:Dictionary>
                      </sap:WorkflowViewStateService.ViewState>
                      <ui:ForEachRow ColumnNames="{x:Null}" CurrentIndex="{x:Null}" DataTable="[dt_uniqueCustomers]" DisplayName="For Each Row in Data Table customers" sap:VirtualizedContainerService.HintSize="1184,5531.33333333333" sap2010:WorkflowViewState.IdRef="ForEachRow_1">
                        <ui:ForEachRow.Body>
                          <ActivityAction x:TypeArguments="sd:DataRow">
                            <ActivityAction.Argument>
                              <DelegateInArgument x:TypeArguments="sd:DataRow" Name="CurrentRow" />
                            </ActivityAction.Argument>
                            <Sequence DisplayName="Body customer data" sap:VirtualizedContainerService.HintSize="1150,5411.33333333333" sap2010:WorkflowViewState.IdRef="Sequence_1">
                              <Sequence.Variables>
                                <Variable x:TypeArguments="sd:DataTable" Name="dt_FilteredCustomers" />
                                <Variable x:TypeArguments="sd:DataTable" Name="dt_emailData" />
                                <Variable x:TypeArguments="x:String" Name="htmlData" />
                                <Variable x:TypeArguments="x:String" Name="dt_OutHTML" />
                              </Sequence.Variables>
                              <sap:WorkflowViewStateService.ViewState>
                                <scg:Dictionary x:TypeArguments="x:String, x:Object">
                                  <x:Boolean x:Key="IsExpanded">True</x:Boolean>
                                </scg:Dictionary>
                              </sap:WorkflowViewStateService.ViewState>
                              <ui:FilterDataTable DataTable="[dt_Initial]" DisplayName="Filter Data Table customers" FilterRowsMode="Keep" sap:VirtualizedContainerService.HintSize="1088,164" sap2010:WorkflowViewState.IdRef="FilterDataTable_1" OutputDataTable="[dt_FilteredCustomers]" SelectColumnsMode="Keep">
                                <ui:FilterDataTable.Filters>
                                  <scg:List x:TypeArguments="ui:FilterOperationArgument" Capacity="4">
                                    <ui:FilterOperationArgument BooleanOperator="And" Operator="EQ">
                                      <ui:FilterOperationArgument.Column>
                                        <InArgument x:TypeArguments="x:String">["Customer"]</InArgument>
                                      </ui:FilterOperationArgument.Column>
                                      <ui:FilterOperationArgument.Operand>
                                        <InArgument x:TypeArguments="x:String">[CurrentRow("Customer").ToString]</InArgument>
                                      </ui:FilterOperationArgument.Operand>
                                    </ui:FilterOperationArgument>
                                  </scg:List>
                                </ui:FilterDataTable.Filters>
                                <ui:FilterDataTable.SelectColumns>
                                  <scg:List x:TypeArguments="InArgument" Capacity="4">
                                    <x:Null />
                                  </scg:List>
                                </ui:FilterDataTable.SelectColumns>
                              </ui:FilterDataTable>
                              <ui:CopyFile ContinueOnError="{x:Null}" Destination="[in_Config(&quot;TransportMissesInputFilesPath&quot;).ToString+&quot;\&quot;+date.today.tostring(&quot;MMMM yyyy&quot;)+&quot;\&quot;+date.Today.ToString(&quot;dd.MM.yyyy&quot;)+&quot;\&quot;+&quot;Customer_&quot;+CurrentRow(&quot;Customer&quot;).ToString+&quot;.xlsx&quot;]" DisplayName="Copy File template" sap:VirtualizedContainerService.HintSize="1088,163.333333333333" sap2010:WorkflowViewState.IdRef="CopyFile_1" Overwrite="True" Path="[in_Config(&quot;InputTemplateFilePath&quot;).ToString]" />
                              <ui:LogMessage DisplayName="Log Message customer file" sap:VirtualizedContainerService.HintSize="1088,96" sap2010:WorkflowViewState.IdRef="LogMessage_2" Level="Info" Message="[in_Config(&quot;TransportMissesInputFilesPath&quot;).ToString+&quot;\&quot;+date.today.tostring(&quot;MMMM yyyy&quot;)+&quot;\&quot;+date.Today.ToString(&quot;dd.MM.yyyy&quot;)+&quot;\&quot;+&quot;Customer_&quot;+CurrentRow(&quot;Customer&quot;).ToString+&quot;.xlsx&quot;]" />
                              <ui:WriteRange AddHeaders="True" DataTable="[dt_FilteredCustomers]" DisplayName="Write Range customer data in Output sheet" sap:VirtualizedContainerService.HintSize="1088,116" sap2010:WorkflowViewState.IdRef="WriteRange_1" SheetName="Output" StartingCell="A1" WorkbookPath="[in_Config(&quot;TransportMissesInputFilesPath&quot;).ToString+&quot;\&quot;+date.today.tostring(&quot;MMMM yyyy&quot;)+&quot;\&quot;+date.Today.ToString(&quot;dd.MM.yyyy&quot;)+&quot;\&quot;+&quot;Customer_&quot;+CurrentRow(&quot;Customer&quot;).ToString+&quot;.xlsx&quot;]" />
                              <ui:ReadRange AddHeaders="True" DataTable="[dt_emailData]" DisplayName="Read Range single customer data" sap:VirtualizedContainerService.HintSize="1088,88" sap2010:WorkflowViewState.IdRef="ReadRange_4" PreserveFormat="True" SheetName="Output" WorkbookPath="[in_Config(&quot;TransportMissesInputFilesPath&quot;).ToString+&quot;\&quot;+date.today.tostring(&quot;MMMM yyyy&quot;)+&quot;\&quot;+date.Today.ToString(&quot;dd.MM.yyyy&quot;)+&quot;\&quot;+&quot;Customer_&quot;+CurrentRow(&quot;Customer&quot;).ToString+&quot;.xlsx&quot;]">
                                <ui:ReadRange.Range>
                                  <InArgument x:TypeArguments="x:String">
                                    <Literal x:TypeArguments="x:String" Value="" />
                                  </InArgument>
                                </ui:ReadRange.Range>
                              </ui:ReadRange>
                              <ui:ExcelApplicationScope Password="{x:Null}" DisplayName="Excel Application scope single customer data" sap:VirtualizedContainerService.HintSize="1088,3374.66666666667" sap2010:WorkflowViewState.IdRef="ExcelApplicationScope_2" InstanceCachePeriod="3000" Visible="False" WorkbookPath="[in_Config(&quot;TransportMissesInputFilesPath&quot;).ToString+&quot;\&quot;+date.today.tostring(&quot;MMMM yyyy&quot;)+&quot;\&quot;+date.Today.ToString(&quot;dd.MM.yyyy&quot;)+&quot;\&quot;+&quot;Customer_&quot;+CurrentRow(&quot;Customer&quot;).ToString+&quot;.xlsx&quot;]">
                                <ui:ExcelApplicationScope.Body>
                                  <ActivityAction x:TypeArguments="ui:WorkbookApplication">
                                    <ActivityAction.Argument>
                                      <DelegateInArgument x:TypeArguments="ui:WorkbookApplication" Name="ExcelWorkbookScope" />
                                    </ActivityAction.Argument>
                                    <Sequence DisplayName="Do" sap:VirtualizedContainerService.HintSize="690.************,3264.66666666667" sap2010:WorkflowViewState.IdRef="Sequence_5">
                                      <sap:WorkflowViewStateService.ViewState>
                                        <scg:Dictionary x:TypeArguments="x:String, x:Object">
                                          <x:Boolean x:Key="IsExpanded">True</x:Boolean>
                                          <x:Boolean x:Key="IsPinned">False</x:Boolean>
                                        </scg:Dictionary>
                                      </sap:WorkflowViewStateService.ViewState>
                                      <ui:ExcelRemoveDuplicatesRange DisplayName="Remove Duplicates Range L to N in email part" sap:VirtualizedContainerService.HintSize="628.************,60" sap2010:WorkflowViewState.IdRef="ExcelRemoveDuplicatesRange_2" Range="[&quot;L1:N&quot;+(dt_emailData.rows.count+1).ToString]" SheetName="Output" />
                                      <ui:ExcelRemoveDuplicatesRange DisplayName="Remove Duplicates Range c to k " sap:VirtualizedContainerService.HintSize="628.************,60" sap2010:WorkflowViewState.IdRef="ExcelRemoveDuplicatesRange_5" Range="[&quot;C1:K&quot;+(dt_emailData.rows.count+1).ToString]" SheetName="Output" />
                                      <ui:ExcelReadRange AddHeaders="True" DataTable="[dt_emailDataOnly]" DisplayName="Read Range l to n email part" sap:VirtualizedContainerService.HintSize="628.************,60" sap2010:WorkflowViewState.IdRef="ExcelReadRange_1" Range="[&quot;L1:N&quot;+(dt_emailData.rows.count+1).ToString]" SheetName="Output" />
                                      <ui:ExcelReadRange AddHeaders="True" DataTable="[dt_emailBodyData]" DisplayName="Read Range c to k  custome details part" sap:VirtualizedContainerService.HintSize="628.************,60" sap2010:WorkflowViewState.IdRef="ExcelReadRange_2" PreserveFormat="True" Range="[&quot;C1:K&quot;+(dt_emailData.rows.count+1).ToString]" SheetName="Output" />
                                      <ui:FilterDataTable DataTable="[dt_emailDataOnly]" DisplayName="Filter Data Table email part" FilterRowsMode="Keep" sap:VirtualizedContainerService.HintSize="628.************,164" sap2010:WorkflowViewState.IdRef="FilterDataTable_3" OutputDataTable="[dt_FilteredEmailDataWhole]" SelectColumnsMode="Keep">
                                        <ui:FilterDataTable.Filters>
                                          <scg:List x:TypeArguments="ui:FilterOperationArgument" Capacity="4">
                                            <ui:FilterOperationArgument BooleanOperator="And" Operator="EQ">
                                              <ui:FilterOperationArgument.Column>
                                                <InArgument x:TypeArguments="x:String">["ID"]</InArgument>
                                              </ui:FilterOperationArgument.Column>
                                              <ui:FilterOperationArgument.Operand>
                                                <InArgument x:TypeArguments="x:String">["1"]</InArgument>
                                              </ui:FilterOperationArgument.Operand>
                                            </ui:FilterOperationArgument>
                                            <ui:FilterOperationArgument BooleanOperator="Or" Operator="EQ">
                                              <ui:FilterOperationArgument.Column>
                                                <InArgument x:TypeArguments="x:String">["#"]</InArgument>
                                              </ui:FilterOperationArgument.Column>
                                              <ui:FilterOperationArgument.Operand>
                                                <InArgument x:TypeArguments="x:String">["X"]</InArgument>
                                              </ui:FilterOperationArgument.Operand>
                                            </ui:FilterOperationArgument>
                                          </scg:List>
                                        </ui:FilterDataTable.Filters>
                                        <ui:FilterDataTable.SelectColumns>
                                          <scg:List x:TypeArguments="InArgument" Capacity="4">
                                            <x:Null />
                                          </scg:List>
                                        </ui:FilterDataTable.SelectColumns>
                                      </ui:FilterDataTable>
                                      <ui:ExcelWriteRange AddHeaders="True" DataTable="[dt_FilteredEmailDataWhole]" DisplayName="Write Range email data initial" sap:VirtualizedContainerService.HintSize="628.************,88" sap2010:WorkflowViewState.IdRef="ExcelWriteRange_1" SheetName="EmailInitial" StartingCell="A1" />
                                      <ui:ForEachRow ColumnNames="{x:Null}" sap2010:Annotation.AnnotationText="to get the email address and read #=either 1 or X and break" CurrentIndex="[rowIndexEmail]" DataTable="[dt_FilteredEmailDataWhole]" DisplayName="For Each Row in Data Table to select first occurence" sap:VirtualizedContainerService.HintSize="628.************,712" sap2010:WorkflowViewState.IdRef="ForEachRow_2">
                                        <ui:ForEachRow.Body>
                                          <ActivityAction x:TypeArguments="sd:DataRow">
                                            <ActivityAction.Argument>
                                              <DelegateInArgument x:TypeArguments="sd:DataRow" Name="CurrentRowEmailDataFilter" />
                                            </ActivityAction.Argument>
                                            <Sequence DisplayName="Body email filters" sap:VirtualizedContainerService.HintSize="594.************,564.************" sap2010:WorkflowViewState.IdRef="Sequence_16">
                                              <sap:WorkflowViewStateService.ViewState>
                                                <scg:Dictionary x:TypeArguments="x:String, x:Object">
                                                  <x:Boolean x:Key="IsExpanded">True</x:Boolean>
                                                </scg:Dictionary>
                                              </sap:WorkflowViewStateService.ViewState>
                                              <If Condition="[CurrentRowEmailDataFilter(&quot;ID&quot;).ToString.Equals(&quot;1&quot;) or CurrentRowEmailDataFilter(&quot;#&quot;).ToString.Equals(&quot;X&quot;)]" DisplayName="If to select email first occurence" sap:VirtualizedContainerService.HintSize="532.************,454" sap2010:WorkflowViewState.IdRef="If_1">
                                                <sap:WorkflowViewStateService.ViewState>
                                                  <scg:Dictionary x:TypeArguments="x:String, x:Object">
                                                    <x:Boolean x:Key="IsExpanded">True</x:Boolean>
                                                    <x:Boolean x:Key="IsPinned">False</x:Boolean>
                                                  </scg:Dictionary>
                                                </sap:WorkflowViewStateService.ViewState>
                                                <If.Then>
                                                  <Sequence DisplayName="Sequence email first occurence" sap:VirtualizedContainerService.HintSize="496,261.333333333333" sap2010:WorkflowViewState.IdRef="Sequence_17">
                                                    <sap:WorkflowViewStateService.ViewState>
                                                      <scg:Dictionary x:TypeArguments="x:String, x:Object">
                                                        <x:Boolean x:Key="IsExpanded">True</x:Boolean>
                                                      </scg:Dictionary>
                                                    </sap:WorkflowViewStateService.ViewState>
                                                    <Assign DisplayName="Assign email recipients" sap:VirtualizedContainerService.HintSize="434,85.3333333333333" sap2010:WorkflowViewState.IdRef="Assign_1">
                                                      <Assign.To>
                                                        <OutArgument x:TypeArguments="x:String">[strEmailRecipients]</OutArgument>
                                                      </Assign.To>
                                                      <Assign.Value>
                                                        <InArgument x:TypeArguments="x:String">[CurrentRowEmailDataFilter("E-mail Address").ToString]</InArgument>
                                                      </Assign.Value>
                                                    </Assign>
                                                    <ui:Break DisplayName="Break if first occurence found" sap:VirtualizedContainerService.HintSize="434,25.3333333333333" sap2010:WorkflowViewState.IdRef="Break_1" />
                                                  </Sequence>
                                                </If.Then>
                                              </If>
                                            </Sequence>
                                          </ActivityAction>
                                        </ui:ForEachRow.Body>
                                        <sap:WorkflowViewStateService.ViewState>
                                          <scg:Dictionary x:TypeArguments="x:String, x:Object">
                                            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
                                            <x:Boolean x:Key="IsPinned">False</x:Boolean>
                                            <x:Boolean x:Key="IsAnnotationDocked">True</x:Boolean>
                                          </scg:Dictionary>
                                        </sap:WorkflowViewStateService.ViewState>
                                      </ui:ForEachRow>
                                      <ui:ExcelReadRange AddHeaders="True" DataTable="[dt_FilteredEmailDataOnly]" DisplayName="Read Range email initial" sap:VirtualizedContainerService.HintSize="628.************,60" sap2010:WorkflowViewState.IdRef="ExcelReadRange_6" Range="[&quot;A1:C&quot;+(rowIndexEmail+2).ToString]" SheetName="EmailInitial" />
                                      <ui:ExcelWriteRange AddHeaders="True" DataTable="[dt_FilteredEmailDataOnly]" DisplayName="Write Range email initial" sap:VirtualizedContainerService.HintSize="628.************,88" sap2010:WorkflowViewState.IdRef="ExcelWriteRange_4" SheetName="EmailFinal" StartingCell="A1" />
                                      <ui:ExcelReadRange AddHeaders="True" DataTable="[dt_AtoK]" DisplayName="Read Range output data" sap:VirtualizedContainerService.HintSize="628.************,60" sap2010:WorkflowViewState.IdRef="ExcelReadRange_3" PreserveFormat="True" Range="[&quot;A1:K&quot;+(dt_emailBodyData.rows.Count+1).ToString]" SheetName="Output" />
                                      <ui:ExcelReadCell Cell="B2" DisplayName="Read Cell customer names" sap:VirtualizedContainerService.HintSize="628.************,60" sap2010:WorkflowViewState.IdRef="ExcelReadCell_1" SheetName="Output">
                                        <ui:ExcelReadCell.Result>
                                          <OutArgument x:TypeArguments="x:String">[strCustomerName]</OutArgument>
                                        </ui:ExcelReadCell.Result>
                                      </ui:ExcelReadCell>
                                      <ui:ExcelWriteRange AddHeaders="True" DataTable="[dt_AtoK]" DisplayName="Write Range collated data customers" sap:VirtualizedContainerService.HintSize="628.************,88" sap2010:WorkflowViewState.IdRef="ExcelWriteRange_2" SheetName="CollatedOutput" StartingCell="A1" />
                                      <ui:ExcelWriteRange AddHeaders="True" DataTable="[dt_FilteredEmailDataOnly]" DisplayName="Write Range email data" sap:VirtualizedContainerService.HintSize="628.************,88" sap2010:WorkflowViewState.IdRef="ExcelWriteRange_3" SheetName="CollatedOutput" StartingCell="L1" />
                                      <ui:ExcelReadRange AddHeaders="True" DataTable="[dt_emailBodyCorrectFormat]" DisplayName="Read Range after removing duplicates" sap:VirtualizedContainerService.HintSize="628.************,60" sap2010:WorkflowViewState.IdRef="ExcelReadRange_4" Range="[&quot;C1:K&quot;+(dt_AtoK.rows.count+1).ToString]" SheetName="CollatedOutput" />
                                      <ui:FilterDataTable DataTable="[dt_emailBodyCorrectFormat]" DisplayName="Filter Data Table remove columns where purchase order is empty" FilterRowsMode="Remove" sap:VirtualizedContainerService.HintSize="628.************,164" sap2010:WorkflowViewState.IdRef="FilterDataTable_5" OutputDataTable="[dt_emailBodyCorrectFormatWithoutSpace]" SelectColumnsMode="Keep">
                                        <ui:FilterDataTable.Filters>
                                          <scg:List x:TypeArguments="ui:FilterOperationArgument" Capacity="4">
                                            <ui:FilterOperationArgument Operand="{x:Null}" BooleanOperator="And" Operator="EMPTY">
                                              <ui:FilterOperationArgument.Column>
                                                <InArgument x:TypeArguments="x:String">["Purchase order"]</InArgument>
                                              </ui:FilterOperationArgument.Column>
                                            </ui:FilterOperationArgument>
                                          </scg:List>
                                        </ui:FilterDataTable.Filters>
                                        <ui:FilterDataTable.SelectColumns>
                                          <scg:List x:TypeArguments="InArgument" Capacity="4">
                                            <x:Null />
                                          </scg:List>
                                        </ui:FilterDataTable.SelectColumns>
                                      </ui:FilterDataTable>
                                      <ui:KillProcess ContinueOnError="{x:Null}" Process="{x:Null}" AppliesTo="OnlyCurrentUser" DisplayName="Kill Process excel before filtering customer names" sap:VirtualizedContainerService.HintSize="628.************,136.************" sap2010:WorkflowViewState.IdRef="KillProcess_1" ProcessName="excel" />
                                      <ui:ForEachRow ColumnNames="{x:Null}" CurrentIndex="{x:Null}" DataTable="[dt_emailBodyCorrectFormatWithoutSpace]" DisplayName="For Each Row in Data Table check date format" sap:VirtualizedContainerService.HintSize="628.************,505.333333333333" sap2010:WorkflowViewState.IdRef="ForEachRow_3">
                                        <ui:ForEachRow.Body>
                                          <ActivityAction x:TypeArguments="sd:DataRow">
                                            <ActivityAction.Argument>
                                              <DelegateInArgument x:TypeArguments="sd:DataRow" Name="CurrentRowdate" />
                                            </ActivityAction.Argument>
                                            <Sequence DisplayName="Body" sap:VirtualizedContainerService.HintSize="546,388.************" sap2010:WorkflowViewState.IdRef="Sequence_19">
                                              <sap:WorkflowViewStateService.ViewState>
                                                <scg:Dictionary x:TypeArguments="x:String, x:Object">
                                                  <x:Boolean x:Key="IsExpanded">True</x:Boolean>
                                                </scg:Dictionary>
                                              </sap:WorkflowViewStateService.ViewState>
                                              <If Condition="[CurrentRowdate(&quot;Revised ETA&quot;).ToString.Contains(&quot;00:00:00&quot;)]" DisplayName="If check revised eta" sap:VirtualizedContainerService.HintSize="484,278" sap2010:WorkflowViewState.IdRef="If_2">
                                                <If.Then>
                                                  <Assign DisplayName="Assign evisedeta" sap:VirtualizedContainerService.HintSize="434,85.3333333333333" sap2010:WorkflowViewState.IdRef="Assign_2">
                                                    <Assign.To>
                                                      <OutArgument x:TypeArguments="x:Object">[CurrentRowdate("Revised ETA")]</OutArgument>
                                                    </Assign.To>
                                                    <Assign.Value>
                                                      <InArgument x:TypeArguments="x:String">[CurrentRowdate("Revised ETA").ToString.Replace("00:00:00","")]</InArgument>
                                                    </Assign.Value>
                                                  </Assign>
                                                </If.Then>
                                                <If.Else>
                                                  <Sequence sap:VirtualizedContainerService.HintSize="242,104" sap2010:WorkflowViewState.IdRef="Sequence_18">
                                                    <sap:WorkflowViewStateService.ViewState>
                                                      <scg:Dictionary x:TypeArguments="x:String, x:Object">
                                                        <x:Boolean x:Key="IsExpanded">True</x:Boolean>
                                                      </scg:Dictionary>
                                                    </sap:WorkflowViewStateService.ViewState>
                                                  </Sequence>
                                                </If.Else>
                                              </If>
                                            </Sequence>
                                          </ActivityAction>
                                        </ui:ForEachRow.Body>
                                        <sap:WorkflowViewStateService.ViewState>
                                          <scg:Dictionary x:TypeArguments="x:String, x:Object">
                                            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
                                            <x:Boolean x:Key="IsPinned">False</x:Boolean>
                                          </scg:Dictionary>
                                        </sap:WorkflowViewStateService.ViewState>
                                      </ui:ForEachRow>
                                    </Sequence>
                                  </ActivityAction>
                                </ui:ExcelApplicationScope.Body>
                                <sap:WorkflowViewStateService.ViewState>
                                  <scg:Dictionary x:TypeArguments="x:String, x:Object">
                                    <x:Boolean x:Key="IsExpanded">True</x:Boolean>
                                    <x:Boolean x:Key="IsPinned">False</x:Boolean>
                                  </scg:Dictionary>
                                </sap:WorkflowViewStateService.ViewState>
                              </ui:ExcelApplicationScope>
                              <d:DataTableToHtml in_StyleCSS="{x:Null}" DisplayName="DataTableToHtml  customer data" sap:VirtualizedContainerService.HintSize="1088,82" sap2010:WorkflowViewState.IdRef="DataTableToHtml_1" in_Inputdata="[dt_emailBodyCorrectFormatWithoutSpace]" out_HTML="[htmlData]" />
                              <ui:Replace BuilderPattern="&lt;table&gt;" DisplayName="Replace green table" sap:VirtualizedContainerService.HintSize="1088,60" sap2010:WorkflowViewState.IdRef="Replace_1" Input="[htmlData]" IsBuilderTabModified="True" Model="[{&quot;Index&quot;:0,&quot;OperationID&quot;:&quot;ADV&quot;,&quot;OperationName&quot;:&quot;Advanced&quot;,&quot;OperationValue&quot;:&quot;&lt;table&gt;&quot;,&quot;QuantifierFirstValue&quot;:&quot;1&quot;,&quot;QuantifierID&quot;:&quot;EXT&quot;,&quot;QuantifierName&quot;:&quot;Exactly&quot;,&quot;QuantifierSecondValue&quot;:null}]" Pattern="&lt;table&gt;" RegexOption="None" Replacement="&lt;table class='GreenTable'&gt;" Result="[DT_OutHTML]" />
                              <If Condition="[dt_FilteredEmailDataWhole.rows.count=0]" DisplayName="If no recipients" sap:VirtualizedContainerService.HintSize="1088,836.************" sap2010:WorkflowViewState.IdRef="If_3">
                                <If.Then>
                                  <Sequence DisplayName="Sequence no recipients" sap:VirtualizedContainerService.HintSize="450,206.************" sap2010:WorkflowViewState.IdRef="Sequence_20">
                                    <sap:WorkflowViewStateService.ViewState>
                                      <scg:Dictionary x:TypeArguments="x:String, x:Object">
                                        <x:Boolean x:Key="IsExpanded">True</x:Boolean>
                                      </scg:Dictionary>
                                    </sap:WorkflowViewStateService.ViewState>
                                    <ui:LogMessage DisplayName="Log Message no recipeints" sap:VirtualizedContainerService.HintSize="388,96" sap2010:WorkflowViewState.IdRef="LogMessage_3" Level="Info" Message="[&quot;No recipients for &quot; + in_Config(&quot;TransportMissesInputFilesPath&quot;).ToString+&quot;\&quot;+date.today.tostring(&quot;MMMM yyyy&quot;)+&quot;\&quot;+date.Today.ToString(&quot;dd.MM.yyyy&quot;)+&quot;\&quot;+&quot;Customer_&quot;+CurrentRow(&quot;Customer&quot;).ToString+&quot;.xlsx&quot;]" />
                                  </Sequence>
                                </If.Then>
                                <If.Else>
                                  <Sequence DisplayName="Sequence recipeints presenst" sap:VirtualizedContainerService.HintSize="592,671.333333333333" sap2010:WorkflowViewState.IdRef="Sequence_21">
                                    <sap:WorkflowViewStateService.ViewState>
                                      <scg:Dictionary x:TypeArguments="x:String, x:Object">
                                        <x:Boolean x:Key="IsExpanded">True</x:Boolean>
                                      </scg:Dictionary>
                                    </sap:WorkflowViewStateService.ViewState>
                                    <uma:Office365ApplicationScope CertificateAsBase64="{x:Null}" CertificatePassword="{x:Null}" ContinueOnError="{x:Null}" OAuth2Username="{x:Null}" Password="{x:Null}" SecureApplicationSecret="{x:Null}" SecurePassword="{x:Null}" Timeout="{x:Null}" Username="{x:Null}" ApplicationId="[in_Config(&quot;MailApplicationID&quot;).ToString]" ApplicationSecret="[in_Config(&quot;MailSecretKey&quot;).ToString]" AuthenticationType="ApplicationIdAndSecret" DisplayName="Microsoft Office 365 Scope filter data present" Environment="Global" sap:VirtualizedContainerService.HintSize="530,560.************" sap2010:WorkflowViewState.IdRef="Office365ApplicationScope_6" OAuthApplication="UiPath" Services="Mail" Tenant="[in_Config(&quot;MailTenantID&quot;).ToString]">
                                      <uma:Office365ApplicationScope.Body>
                                        <ActivityAction x:TypeArguments="mg:GraphServiceClient">
                                          <ActivityAction.Argument>
                                            <DelegateInArgument x:TypeArguments="mg:GraphServiceClient" Name="ParentScope" />
                                          </ActivityAction.Argument>
                                          <Sequence DisplayName="Do- send mail for input data present" sap:VirtualizedContainerService.HintSize="496,473.333333333333" sap2010:WorkflowViewState.IdRef="Sequence_22">
                                            <sap:WorkflowViewStateService.ViewState>
                                              <scg:Dictionary x:TypeArguments="x:String, x:Object">
                                                <x:Boolean x:Key="IsExpanded">True</x:Boolean>
                                              </scg:Dictionary>
                                            </sap:WorkflowViewStateService.ViewState>
                                            <umam:SendMail AttachmentsCollection="{x:Null}" Cc="{x:Null}" From="{x:Null}" ReplyTo="{x:Null}" Account="[in_config(&quot;EmailFromAccount&quot;).ToString]" Bcc="[in_Config(&quot;EmailRecipients&quot;).ToString.Split(New Char() {&quot;;&quot;c})]" Body="[in_Config(&quot;EmailBodyTestRahmi3&quot;).ToString.Replace(&quot;*CUSTOMER NAME*&quot;,strCustomerName)+dt_OutHTML+in_Config(&quot;EmailBodyAccessSchneiderButton&quot;).ToString+in_Config(&quot;EmailBodyFooter&quot;).ToString]" DisplayName="Send Mail customer details" sap:VirtualizedContainerService.HintSize="434,362.************" sap2010:WorkflowViewState.IdRef="SendMail_9" Importance="Normal" IsBodyHTML="True" IsDraft="False" Subject="[in_Config(&quot;EmailSubject&quot;).ToString]" To="[strEmailRecipients.Split(New Char() {&quot;;&quot;c})]">
                                              <umam:SendMail.Attachments>
                                                <scg:List x:TypeArguments="InArgument(x:String)" Capacity="0" />
                                              </umam:SendMail.Attachments>
                                            </umam:SendMail>
                                          </Sequence>
                                        </ActivityAction>
                                      </uma:Office365ApplicationScope.Body>
                                      <sap:WorkflowViewStateService.ViewState>
                                        <scg:Dictionary x:TypeArguments="x:String, x:Object">
                                          <x:Boolean x:Key="IsExpanded">True</x:Boolean>
                                          <x:Boolean x:Key="IsPinned">False</x:Boolean>
                                        </scg:Dictionary>
                                      </sap:WorkflowViewStateService.ViewState>
                                    </uma:Office365ApplicationScope>
                                  </Sequence>
                                </If.Else>
                              </If>
                            </Sequence>
                          </ActivityAction>
                        </ui:ForEachRow.Body>
                        <sap:WorkflowViewStateService.ViewState>
                          <scg:Dictionary x:TypeArguments="x:String, x:Object">
                            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
                          </scg:Dictionary>
                        </sap:WorkflowViewStateService.ViewState>
                      </ui:ForEachRow>
                    </FlowStep>
                  </FlowStep.Next>
                </FlowStep>
              </FlowStep.Next>
            </FlowStep>
          </FlowStep.Next>
        </FlowStep>
      </FlowStep.Next>
    </FlowStep>
    <x:Reference>__ReferenceID0</x:Reference>
    <x:Reference>__ReferenceID1</x:Reference>
    <x:Reference>__ReferenceID2</x:Reference>
    <FlowStep x:Name="__ReferenceID5">
      <sap:WorkflowViewStateService.ViewState>
        <scg:Dictionary x:TypeArguments="x:String, x:Object">
          <av:Point x:Key="ShapeLocation">350,63.6************</av:Point>
          <av:Size x:Key="ShapeSize">200,54</av:Size>
          <av:PointCollection x:Key="ConnectorLocation">450,117.************ 450,151.02 400,151.02</av:PointCollection>
        </scg:Dictionary>
      </sap:WorkflowViewStateService.ViewState>
      <ui:ReadRange AddHeaders="True" DataTable="[dt_Complete]" DisplayName="Read Range Full RPA renamed sheet" sap:VirtualizedContainerService.HintSize="200,54" sap2010:WorkflowViewState.IdRef="ReadRange_5" SheetName="RPARenamed" WorkbookPath="[in_Config(&quot;TransportMissesInputFilesPath&quot;).ToString+&quot;\&quot;+date.today.tostring(&quot;MMMM yyyy&quot;)+&quot;\&quot;+date.Today.ToString(&quot;dd.MM.yyyy&quot;)+&quot;\&quot;+&quot;Transport misses &quot;+date.Today.ToString(&quot;dd.MM.yyyy&quot;) +&quot;.XLSX&quot;]">
        <ui:ReadRange.Range>
          <InArgument x:TypeArguments="x:String">
            <Literal x:TypeArguments="x:String" Value="" />
          </InArgument>
        </ui:ReadRange.Range>
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </ui:ReadRange>
      <FlowStep.Next>
        <x:Reference>__ReferenceID3</x:Reference>
      </FlowStep.Next>
    </FlowStep>
    <x:Reference>__ReferenceID4</x:Reference>
  </Flowchart>
</Activity>