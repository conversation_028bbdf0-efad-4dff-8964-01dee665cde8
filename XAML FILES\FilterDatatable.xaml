<Activity mc:Ignorable="sap sap2010" x:Class="FilterDatatable" xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities" xmlns:av="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities" xmlns:s="clr-namespace:System;assembly=mscorlib" xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation" xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation" xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib" xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib" xmlns:sd="clr-namespace:System.Data;assembly=System.Data" xmlns:ui="http://schemas.uipath.com/workflow/activities" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="in_Config" Type="InArgument(scg:Dictionary(x:String, x:Object))" />
    <x:Property Name="in_STFilename" Type="InArgument(x:String)" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap:VirtualizedContainerService.HintSize>883.333333333333,780.666666666667</sap:VirtualizedContainerService.HintSize>
  <sap2010:WorkflowViewState.IdRef>FilterDatatable_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <scg:List x:TypeArguments="x:String" Capacity="62">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Microsoft.VisualBasic</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>System</x:String>
      <x:String>System.Collections</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Data</x:String>
      <x:String>System.Diagnostics</x:String>
      <x:String>System.Drawing</x:String>
      <x:String>System.IO</x:String>
      <x:String>System.Linq</x:String>
      <x:String>System.Net.Mail</x:String>
      <x:String>System.Xml</x:String>
      <x:String>System.Xml.Linq</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>UiPath.Core</x:String>
      <x:String>UiPath.Core.Activities</x:String>
      <x:String>System.ComponentModel</x:String>
      <x:String>System.Runtime.Serialization</x:String>
      <x:String>System.Xml.Serialization</x:String>
      <x:String>UiPath.Excel.Activities</x:String>
      <x:String>UiPath.Excel</x:String>
      <x:String>System.Reflection</x:String>
      <x:String>System.Runtime.InteropServices</x:String>
      <x:String>System.Collections.ObjectModel</x:String>
      <x:String>System.Activities.DynamicUpdate</x:String>
      <x:String>UiPath.DataTableUtilities</x:String>
      <x:String>System.Globalization</x:String>
    </scg:List>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>System.Collections.Immutable</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>System.ComponentModel.TypeConverter</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>System.Data</AssemblyReference>
      <AssemblyReference>System.Data.DataSetExtensions</AssemblyReference>
      <AssemblyReference>System.Data.Entity</AssemblyReference>
      <AssemblyReference>System.Drawing</AssemblyReference>
      <AssemblyReference>System.Linq</AssemblyReference>
      <AssemblyReference>System.Linq.Async</AssemblyReference>
      <AssemblyReference>System.Linq.Async.Queryable</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
      <AssemblyReference>System.ObjectModel</AssemblyReference>
      <AssemblyReference>System.Runtime.Serialization</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System.ValueTuple</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>System.Xml</AssemblyReference>
      <AssemblyReference>System.Xml.Linq</AssemblyReference>
      <AssemblyReference>UiPath.Excel</AssemblyReference>
      <AssemblyReference>UiPath.Excel.Activities</AssemblyReference>
      <AssemblyReference>UiPath.Excel.Activities.Design</AssemblyReference>
      <AssemblyReference>UiPath.MicrosoftOffice365.Activities.Design</AssemblyReference>
      <AssemblyReference>UiPath.Studio.Constants</AssemblyReference>
      <AssemblyReference>UiPath.System.Activities</AssemblyReference>
      <AssemblyReference>UiPath.System.Activities.Design</AssemblyReference>
      <AssemblyReference>UiPath.UiAutomation.Activities</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Flowchart sap2010:Annotation.AnnotationText="1. Read ST file&#xA;2. Filter ST file with respect to Article Number, ETA Date , Last Article Event Subclass columns&#xA;3. Prepare columns with article number plus 00 at front and without 00, revised ETA and product code&#xA;4. Check for the Product code and update the Revised ETA " DisplayName="FilterDatatable" sap:VirtualizedContainerService.HintSize="634,716" sap2010:WorkflowViewState.IdRef="Flowchart_1">
    <Flowchart.Variables>
      <Variable x:TypeArguments="ui:WorkbookApplication" Name="varWb" />
      <Variable x:TypeArguments="sd:DataTable" Name="dt_STTableInitial" />
      <Variable x:TypeArguments="sd:DataTable" Name="dt_STTableFinal" />
      <Variable x:TypeArguments="sd:DataTable" Name="dt_AppendRange" />
      <Variable x:TypeArguments="x:String" Name="ETADatePlusTwo" />
      <Variable x:TypeArguments="x:String" Name="ETADatePlusOne" />
      <Variable x:TypeArguments="x:Int32" Default="0" Name="rowIndex" />
      <Variable x:TypeArguments="x:String" Name="combinedStrEta" />
    </Flowchart.Variables>
    <sap:WorkflowViewStateService.ViewState>
      <scg:Dictionary x:TypeArguments="x:String, x:Object">
        <x:Boolean x:Key="IsExpanded">True</x:Boolean>
        <av:Point x:Key="ShapeLocation">270,2.5</av:Point>
        <av:Size x:Key="ShapeSize">60,74.6666666666667</av:Size>
        <av:PointCollection x:Key="ConnectorLocation">300,77.1666666666667 300,110.166666666667 240,110.166666666667</av:PointCollection>
        <x:Boolean x:Key="IsAnnotationDocked">True</x:Boolean>
      </scg:Dictionary>
    </sap:WorkflowViewStateService.ViewState>
    <Flowchart.StartNode>
      <x:Reference>__ReferenceID5</x:Reference>
    </Flowchart.StartNode>
    <FlowStep x:Name="__ReferenceID1">
      <sap:WorkflowViewStateService.ViewState>
        <scg:Dictionary x:TypeArguments="x:String, x:Object">
          <av:Point x:Key="ShapeLocation">200,184.02</av:Point>
          <av:Size x:Key="ShapeSize">200,54</av:Size>
          <av:PointCollection x:Key="ConnectorLocation">300,238.02 300,253.666666666667</av:PointCollection>
        </scg:Dictionary>
      </sap:WorkflowViewStateService.ViewState>
      <ui:LogMessage DisplayName="Log Message excel data with required columns" sap:VirtualizedContainerService.HintSize="200,54" sap2010:WorkflowViewState.IdRef="LogMessage_2" Level="Info" Message="[&quot;Completed preparing ST file data with required columns&quot;]">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </ui:LogMessage>
      <FlowStep.Next>
        <FlowStep x:Name="__ReferenceID0">
          <sap:WorkflowViewStateService.ViewState>
            <scg:Dictionary x:TypeArguments="x:String, x:Object">
              <av:Point x:Key="ShapeLocation">200,253.666666666667</av:Point>
              <av:Size x:Key="ShapeSize">200,54</av:Size>
              <av:PointCollection x:Key="ConnectorLocation">300,307.666666666667 300,357.666666666667</av:PointCollection>
            </scg:Dictionary>
          </sap:WorkflowViewStateService.ViewState>
          <ui:ExcelCloseWorkbook DisplayName="Close Workbook ST file" sap:VirtualizedContainerService.HintSize="200,54" sap2010:WorkflowViewState.IdRef="ExcelCloseWorkbook_1" Workbook="[varWb]">
            <sap:WorkflowViewStateService.ViewState>
              <scg:Dictionary x:TypeArguments="x:String, x:Object">
                <x:Boolean x:Key="IsExpanded">True</x:Boolean>
              </scg:Dictionary>
            </sap:WorkflowViewStateService.ViewState>
          </ui:ExcelCloseWorkbook>
          <FlowStep.Next>
            <FlowStep x:Name="__ReferenceID4">
              <sap:WorkflowViewStateService.ViewState>
                <scg:Dictionary x:TypeArguments="x:String, x:Object">
                  <av:Point x:Key="ShapeLocation">200,357.666666666667</av:Point>
                  <av:Size x:Key="ShapeSize">200,52</av:Size>
                </scg:Dictionary>
              </sap:WorkflowViewStateService.ViewState>
              <ui:KillProcess AppliesTo="{x:Null}" Process="{x:Null}" ContinueOnError="True" DisplayName="Kill Process" sap:VirtualizedContainerService.HintSize="200,52" sap2010:WorkflowViewState.IdRef="KillProcess_2" ProcessName="EXCEL">
                <sap:WorkflowViewStateService.ViewState>
                  <scg:Dictionary x:TypeArguments="x:String, x:Object">
                    <x:Boolean x:Key="IsExpanded">True</x:Boolean>
                  </scg:Dictionary>
                </sap:WorkflowViewStateService.ViewState>
              </ui:KillProcess>
            </FlowStep>
          </FlowStep.Next>
        </FlowStep>
      </FlowStep.Next>
    </FlowStep>
    <x:Reference>__ReferenceID0</x:Reference>
    <FlowStep x:Name="__ReferenceID3">
      <sap:WorkflowViewStateService.ViewState>
        <scg:Dictionary x:TypeArguments="x:String, x:Object">
          <av:Point x:Key="ShapeLocation">370,3.33333333333333</av:Point>
          <av:Size x:Key="ShapeSize">200,54</av:Size>
          <av:PointCollection x:Key="ConnectorLocation">470,57.3333333333333 470,88.3333333333333</av:PointCollection>
        </scg:Dictionary>
      </sap:WorkflowViewStateService.ViewState>
      <TryCatch sap:VirtualizedContainerService.HintSize="200,54" sap2010:WorkflowViewState.IdRef="TryCatch_1">
        <TryCatch.Try>
          <ui:ReadRange AddHeaders="True" DataTable="[dt_STTableInitial]" DisplayName="Read Range ST File sheet 1" sap:VirtualizedContainerService.HintSize="334,88" sap2010:WorkflowViewState.IdRef="ReadRange_1" SheetName="Sheet1" WorkbookPath="[in_STFilename]">
            <ui:ReadRange.Range>
              <InArgument x:TypeArguments="x:String">
                <Literal x:TypeArguments="x:String" Value="" />
              </InArgument>
            </ui:ReadRange.Range>
            <sap:WorkflowViewStateService.ViewState>
              <scg:Dictionary x:TypeArguments="x:String, x:Object">
                <x:Boolean x:Key="IsExpanded">True</x:Boolean>
              </scg:Dictionary>
            </sap:WorkflowViewStateService.ViewState>
          </ui:ReadRange>
        </TryCatch.Try>
        <TryCatch.Catches>
          <Catch x:TypeArguments="s:Exception" sap:VirtualizedContainerService.HintSize="400,157" sap2010:WorkflowViewState.IdRef="Catch`1_1">
            <sap:WorkflowViewStateService.ViewState>
              <scg:Dictionary x:TypeArguments="x:String, x:Object">
                <x:Boolean x:Key="IsExpanded">True</x:Boolean>
                <x:Boolean x:Key="IsPinned">False</x:Boolean>
              </scg:Dictionary>
            </sap:WorkflowViewStateService.ViewState>
            <ActivityAction x:TypeArguments="s:Exception">
              <ActivityAction.Argument>
                <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
              </ActivityAction.Argument>
              <ui:ReadRange AddHeaders="True" DataTable="[dt_STTableInitial]" DisplayName="Read Range ST File sheet 1" sap:VirtualizedContainerService.HintSize="334,88" sap2010:WorkflowViewState.IdRef="ReadRange_2" SheetName="Sheet12" WorkbookPath="[in_STFilename]">
                <ui:ReadRange.Range>
                  <InArgument x:TypeArguments="x:String">
                    <Literal x:TypeArguments="x:String" Value="" />
                  </InArgument>
                </ui:ReadRange.Range>
                <sap:WorkflowViewStateService.ViewState>
                  <scg:Dictionary x:TypeArguments="x:String, x:Object">
                    <x:Boolean x:Key="IsExpanded">True</x:Boolean>
                  </scg:Dictionary>
                </sap:WorkflowViewStateService.ViewState>
              </ui:ReadRange>
            </ActivityAction>
          </Catch>
        </TryCatch.Catches>
      </TryCatch>
      <FlowStep.Next>
        <FlowStep x:Name="__ReferenceID2">
          <sap:WorkflowViewStateService.ViewState>
            <scg:Dictionary x:TypeArguments="x:String, x:Object">
              <av:Point x:Key="ShapeLocation">370,88.3333333333333</av:Point>
              <av:Size x:Key="ShapeSize">200,84.6666666666667</av:Size>
              <av:PointCollection x:Key="ConnectorLocation">470,173 470,211.02 400,211.02</av:PointCollection>
            </scg:Dictionary>
          </sap:WorkflowViewStateService.ViewState>
          <Sequence DisplayName="Do Filtering of ST file required columns" sap:VirtualizedContainerService.HintSize="642,3193.33333333333" sap2010:WorkflowViewState.IdRef="Sequence_11">
            <sap:WorkflowViewStateService.ViewState>
              <scg:Dictionary x:TypeArguments="x:String, x:Object">
                <x:Boolean x:Key="IsExpanded">False</x:Boolean>
                <x:Boolean x:Key="IsPinned">False</x:Boolean>
              </scg:Dictionary>
            </sap:WorkflowViewStateService.ViewState>
            <Assign DisplayName="Assign ETA date plus two" sap:VirtualizedContainerService.HintSize="580,85.3333333333333" sap2010:WorkflowViewState.IdRef="Assign_10">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[ETADatePlusTwo]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">[date.Today.AddDays(2).ToString("dd/MM/yyyy")]</InArgument>
              </Assign.Value>
            </Assign>
            <Assign DisplayName="Assign ETA date plus one" sap:VirtualizedContainerService.HintSize="580,85.3333333333333" sap2010:WorkflowViewState.IdRef="Assign_11">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[ETADatePlusOne]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">[date.Today.AddDays(1).ToString("dd/MM/yyyy")]</InArgument>
              </Assign.Value>
            </Assign>
            <Assign DisplayName="Assign combined ETA" sap:VirtualizedContainerService.HintSize="580,85.3333333333333" sap2010:WorkflowViewState.IdRef="Assign_12">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[combinedStrEta]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">[ETADatePlusOne+ ETADatePlusTwo]</InArgument>
              </Assign.Value>
            </Assign>
            <ui:FilterDataTable DataTable="[dt_STTableInitial]" DisplayName="Filter Data Table Article number and ETA date" FilterRowsMode="Keep" sap:VirtualizedContainerService.HintSize="580,164" sap2010:WorkflowViewState.IdRef="FilterDataTable_6" OutputDataTable="[dt_STTableInitial]" SelectColumnsMode="Keep">
              <ui:FilterDataTable.Filters>
                <scg:List x:TypeArguments="ui:FilterOperationArgument" Capacity="4">
                  <ui:FilterOperationArgument BooleanOperator="And" Operator="STARTSWITH">
                    <ui:FilterOperationArgument.Column>
                      <InArgument x:TypeArguments="x:String">["Article Number"]</InArgument>
                    </ui:FilterOperationArgument.Column>
                    <ui:FilterOperationArgument.Operand>
                      <InArgument x:TypeArguments="x:String">["00"]</InArgument>
                    </ui:FilterOperationArgument.Operand>
                  </ui:FilterOperationArgument>
                  <ui:FilterOperationArgument BooleanOperator="And" Operator="STARTSWITH">
                    <ui:FilterOperationArgument.Column>
                      <InArgument x:TypeArguments="x:String">["ETA Date"]</InArgument>
                    </ui:FilterOperationArgument.Column>
                    <ui:FilterOperationArgument.Operand>
                      <InArgument x:TypeArguments="x:String">[DateTime.now.ToString("yyyy/MM/dd")]</InArgument>
                    </ui:FilterOperationArgument.Operand>
                  </ui:FilterOperationArgument>
                </scg:List>
              </ui:FilterDataTable.Filters>
              <ui:FilterDataTable.SelectColumns>
                <scg:List x:TypeArguments="InArgument" Capacity="4">
                  <x:Null />
                </scg:List>
              </ui:FilterDataTable.SelectColumns>
              <sap:WorkflowViewStateService.ViewState>
                <scg:Dictionary x:TypeArguments="x:String, x:Object">
                  <x:Boolean x:Key="IsExpanded">True</x:Boolean>
                </scg:Dictionary>
              </sap:WorkflowViewStateService.ViewState>
            </ui:FilterDataTable>
            <If Condition="[dt_STTableInitial.rows.Count&gt;1]" DisplayName="If ST File no records" sap:VirtualizedContainerService.HintSize="580,261.333333333333" sap2010:WorkflowViewState.IdRef="If_3">
              <If.Then>
                <ui:LogMessage DisplayName="Log Message ST file no record" sap:VirtualizedContainerService.HintSize="334,96" sap2010:WorkflowViewState.IdRef="LogMessage_5" Level="Info" Message="[&quot;ST File contains records&quot;]" />
              </If.Then>
              <If.Else>
                <Throw DisplayName="Throw ST File no record" Exception="[new BusinessRuleException(&quot;ST File has no records, check for holidays&quot;)]" sap:VirtualizedContainerService.HintSize="200,25.3333333333333" sap2010:WorkflowViewState.IdRef="Throw_1" />
              </If.Else>
            </If>
            <ui:FilterDataTable DataTable="[dt_STTableInitial]" DisplayName="Filter Data Table last article event subclass" FilterRowsMode="Remove" sap:VirtualizedContainerService.HintSize="580,164" sap2010:WorkflowViewState.IdRef="FilterDataTable_7" OutputDataTable="[dt_STTableFinal]" SelectColumnsMode="Keep">
              <ui:FilterDataTable.Filters>
                <scg:List x:TypeArguments="ui:FilterOperationArgument" Capacity="4">
                  <ui:FilterOperationArgument BooleanOperator="And" Operator="CONTAINS">
                    <ui:FilterOperationArgument.Column>
                      <InArgument x:TypeArguments="x:String">["Last Article Event Subclass"]</InArgument>
                    </ui:FilterOperationArgument.Column>
                    <ui:FilterOperationArgument.Operand>
                      <InArgument x:TypeArguments="x:String">["Delivery_Success"]</InArgument>
                    </ui:FilterOperationArgument.Operand>
                  </ui:FilterOperationArgument>
                  <ui:FilterOperationArgument BooleanOperator="Or" Operator="CONTAINS">
                    <ui:FilterOperationArgument.Column>
                      <InArgument x:TypeArguments="x:String">["Last Article Event Subclass"]</InArgument>
                    </ui:FilterOperationArgument.Column>
                    <ui:FilterOperationArgument.Operand>
                      <InArgument x:TypeArguments="x:String">["Manifesting_Manifested"]</InArgument>
                    </ui:FilterOperationArgument.Operand>
                  </ui:FilterOperationArgument>
                  <ui:FilterOperationArgument Operand="{x:Null}" BooleanOperator="Or" Operator="EMPTY">
                    <ui:FilterOperationArgument.Column>
                      <InArgument x:TypeArguments="x:String">["Last Article Event Subclass"]</InArgument>
                    </ui:FilterOperationArgument.Column>
                  </ui:FilterOperationArgument>
                </scg:List>
              </ui:FilterDataTable.Filters>
              <ui:FilterDataTable.SelectColumns>
                <scg:List x:TypeArguments="InArgument" Capacity="4">
                  <x:Null />
                </scg:List>
              </ui:FilterDataTable.SelectColumns>
              <sap:WorkflowViewStateService.ViewState>
                <scg:Dictionary x:TypeArguments="x:String, x:Object">
                  <x:Boolean x:Key="IsExpanded">True</x:Boolean>
                </scg:Dictionary>
              </sap:WorkflowViewStateService.ViewState>
            </ui:FilterDataTable>
            <ui:ForEachRow ColumnNames="{x:Null}" CurrentIndex="{x:Null}" DataTable="[dt_STTableFinal]" DisplayName="For Each Row in Data Table to format HUs" sap:VirtualizedContainerService.HintSize="580,472" sap2010:WorkflowViewState.IdRef="ForEachRow_6">
              <ui:ForEachRow.Body>
                <ActivityAction x:TypeArguments="sd:DataRow">
                  <ActivityAction.Argument>
                    <DelegateInArgument x:TypeArguments="sd:DataRow" Name="CurrentRow" />
                  </ActivityAction.Argument>
                  <Sequence sap2010:Annotation.AnnotationText="Format HUs to remove 00 from first column and to add 00 to second column" DisplayName="Body" sap:VirtualizedContainerService.HintSize="496,352" sap2010:WorkflowViewState.IdRef="Sequence_12">
                    <sap:WorkflowViewStateService.ViewState>
                      <scg:Dictionary x:TypeArguments="x:String, x:Object">
                        <x:Boolean x:Key="IsExpanded">True</x:Boolean>
                        <x:Boolean x:Key="IsAnnotationDocked">True</x:Boolean>
                      </scg:Dictionary>
                    </sap:WorkflowViewStateService.ViewState>
                    <Assign DisplayName="Assign column 0 with HU numbers without zero" sap:VirtualizedContainerService.HintSize="434,85.3333333333333" sap2010:WorkflowViewState.IdRef="Assign_16">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:Object">[CurrentRow(0)]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[Convert.ToInt64(CurrentRow(0)).ToString]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <Assign DisplayName="Assign column 1 with Hu numbers with 00 in front" sap:VirtualizedContainerService.HintSize="434,85.3333333333333" sap2010:WorkflowViewState.IdRef="Assign_17">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:Object">[CurrentRow(1)]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">["00"+(CurrentRow(0).ToString.Replace("'",""))]</InArgument>
                      </Assign.Value>
                    </Assign>
                  </Sequence>
                </ActivityAction>
              </ui:ForEachRow.Body>
              <sap:WorkflowViewStateService.ViewState>
                <scg:Dictionary x:TypeArguments="x:String, x:Object">
                  <x:Boolean x:Key="IsExpanded">True</x:Boolean>
                  <x:Boolean x:Key="IsPinned">False</x:Boolean>
                </scg:Dictionary>
              </sap:WorkflowViewStateService.ViewState>
            </ui:ForEachRow>
            <InvokeMethod DisplayName="Invoke Method set ordinal change position column" sap:VirtualizedContainerService.HintSize="580,138.666666666667" sap2010:WorkflowViewState.IdRef="InvokeMethod_3" MethodName="SetOrdinal">
              <InvokeMethod.TargetObject>
                <InArgument x:TypeArguments="sd:DataColumn">[dt_STTableFinal.Columns("Product Code")]</InArgument>
              </InvokeMethod.TargetObject>
              <InArgument x:TypeArguments="x:Int32">1</InArgument>
            </InvokeMethod>
            <ui:FilterDataTable DataTable="[dt_STTableFinal]" DisplayName="Filter Data Table only keep three columns" FilterRowsMode="Keep" sap:VirtualizedContainerService.HintSize="580,164" sap2010:WorkflowViewState.IdRef="FilterDataTable_8" OutputDataTable="[dt_AppendRange]" SelectColumnsMode="Keep">
              <ui:FilterDataTable.Filters>
                <scg:List x:TypeArguments="ui:FilterOperationArgument" Capacity="4">
                  <ui:FilterOperationArgument Column="{x:Null}" Operand="{x:Null}" BooleanOperator="And" Operator="LT" />
                </scg:List>
              </ui:FilterDataTable.Filters>
              <ui:FilterDataTable.SelectColumns>
                <scg:List x:TypeArguments="InArgument" Capacity="4">
                  <InArgument x:TypeArguments="x:String">["Article Number"]</InArgument>
                  <InArgument x:TypeArguments="x:String">["Product Code"]</InArgument>
                  <InArgument x:TypeArguments="x:String">["Connote Number"]</InArgument>
                </scg:List>
              </ui:FilterDataTable.SelectColumns>
            </ui:FilterDataTable>
            <ui:AddDataColumn x:TypeArguments="x:Object" AllowDBNull="{x:Null}" AutoIncrement="{x:Null}" Column="{x:Null}" DefaultValue="{x:Null}" MaxLength="{x:Null}" Unique="{x:Null}" ColumnName="Revised ETA for SAP" DataTable="[dt_AppendRange]" DisplayName="Add Data Column Revised ETA for SAP" sap:VirtualizedContainerService.HintSize="580,191.333333333333" sap2010:WorkflowViewState.IdRef="AddDataColumn`1_2" />
            <ui:WriteRange AddHeaders="True" DataTable="[dt_AppendRange]" DisplayName="Write Range filtered ST rows" sap:VirtualizedContainerService.HintSize="580,116" sap2010:WorkflowViewState.IdRef="WriteRange_1" SheetName="Sheet12" StartingCell="A1" WorkbookPath="[in_Config(&quot;TransportMissesInputFilesPath&quot;).ToString+&quot;\&quot;+date.today.tostring(&quot;MMMM yyyy&quot;)+&quot;\&quot;+date.Today.ToString(&quot;dd.MM.yyyy&quot;)+&quot;\&quot;+&quot;ST File &quot;+date.Today.ToString(&quot;dd.MM.yyyy&quot;) +&quot;.xlsx&quot;]" />
            <ui:LogMessage DisplayName="Log Message datatable count" sap:VirtualizedContainerService.HintSize="580,96" sap2010:WorkflowViewState.IdRef="LogMessage_4" Level="Info" Message="[dt_AppendRange.rows.Count.ToString]" />
            <ui:KillProcess ContinueOnError="{x:Null}" Process="{x:Null}" AppliesTo="OnlyCurrentSession" DisplayName="Kill Process excel" sap:VirtualizedContainerService.HintSize="580,136.666666666667" sap2010:WorkflowViewState.IdRef="KillProcess_1" ProcessName="excel" />
            <ui:ExcelApplicationScope Password="{x:Null}" DisplayName="Excel Application Scope to write the date values" sap:VirtualizedContainerService.HintSize="580,402.666666666667" sap2010:WorkflowViewState.IdRef="ExcelApplicationScope_2" InstanceCachePeriod="3000" Workbook="[varWb]" WorkbookPath="[in_Config(&quot;TransportMissesInputFilesPath&quot;).ToString+&quot;\&quot;+date.today.tostring(&quot;MMMM yyyy&quot;)+&quot;\&quot;+date.Today.ToString(&quot;dd.MM.yyyy&quot;)+&quot;\&quot;+&quot;ST File &quot;+date.Today.ToString(&quot;dd.MM.yyyy&quot;) +&quot;.xlsx&quot;]">
              <ui:ExcelApplicationScope.Body>
                <ActivityAction x:TypeArguments="ui:WorkbookApplication">
                  <ActivityAction.Argument>
                    <DelegateInArgument x:TypeArguments="ui:WorkbookApplication" Name="ExcelWorkbookScope" />
                  </ActivityAction.Argument>
                  <Sequence DisplayName="Do" sap:VirtualizedContainerService.HintSize="514,292.666666666667" sap2010:WorkflowViewState.IdRef="Sequence_13">
                    <sap:WorkflowViewStateService.ViewState>
                      <scg:Dictionary x:TypeArguments="x:String, x:Object">
                        <x:Boolean x:Key="IsExpanded">True</x:Boolean>
                        <x:Boolean x:Key="IsPinned">False</x:Boolean>
                      </scg:Dictionary>
                    </sap:WorkflowViewStateService.ViewState>
                    <ui:ExcelWriteCell Cell="C1" DisplayName="Write Cell C1" sap:VirtualizedContainerService.HintSize="452,88" sap2010:WorkflowViewState.IdRef="ExcelWriteCell_5" SheetName="Sheet12" Text="Article Number with 00" />
                    <ui:ForEachRow ColumnNames="{x:Null}" CurrentIndex="[rowIndex]" DataTable="[dt_AppendRange]" DisplayName="For Each Row in Data Table reference table to update Revised Date" sap:VirtualizedContainerService.HintSize="452,54" sap2010:WorkflowViewState.IdRef="ForEachRow_5">
                      <ui:ForEachRow.Body>
                        <ActivityAction x:TypeArguments="sd:DataRow">
                          <ActivityAction.Argument>
                            <DelegateInArgument x:TypeArguments="sd:DataRow" Name="row" />
                          </ActivityAction.Argument>
                          <Sequence DisplayName="Body to store revised date for SAP" sap:VirtualizedContainerService.HintSize="776,391.333333333333" sap2010:WorkflowViewState.IdRef="Sequence_10">
                            <sap:WorkflowViewStateService.ViewState>
                              <scg:Dictionary x:TypeArguments="x:String, x:Object">
                                <x:Boolean x:Key="IsExpanded">True</x:Boolean>
                              </scg:Dictionary>
                            </sap:WorkflowViewStateService.ViewState>
                            <If sap2010:Annotation.AnnotationText="1. Check if Product code is EXP then revised date is date plus two else date is plus one" Condition="[row(&quot;Product Code&quot;).Equals(&quot;EXP&quot;)]" DisplayName="If check product code" sap:VirtualizedContainerService.HintSize="714,280.666666666667" sap2010:WorkflowViewState.IdRef="If_2">
                              <sap:WorkflowViewStateService.ViewState>
                                <scg:Dictionary x:TypeArguments="x:String, x:Object">
                                  <x:Boolean x:Key="IsAnnotationDocked">True</x:Boolean>
                                </scg:Dictionary>
                              </sap:WorkflowViewStateService.ViewState>
                              <If.Then>
                                <ui:ExcelWriteCell Cell="[&quot;D&quot;+(rowIndex+2).ToString]" DisplayName="Write Cell if EXP" sap:VirtualizedContainerService.HintSize="334,88" sap2010:WorkflowViewState.IdRef="ExcelWriteCell_3" SheetName="Sheet12" Text="[ETADatePlusTwo]" />
                              </If.Then>
                              <If.Else>
                                <ui:ExcelWriteCell Cell="[&quot;D&quot;+(rowIndex+2).ToString]" DisplayName="Write Cell if not EXP" sap:VirtualizedContainerService.HintSize="334,88" sap2010:WorkflowViewState.IdRef="ExcelWriteCell_4" SheetName="Sheet12" Text="[ETADatePlusOne]" />
                              </If.Else>
                            </If>
                          </Sequence>
                        </ActivityAction>
                      </ui:ForEachRow.Body>
                      <sap:WorkflowViewStateService.ViewState>
                        <scg:Dictionary x:TypeArguments="x:String, x:Object">
                          <x:Boolean x:Key="IsExpanded">False</x:Boolean>
                          <x:Boolean x:Key="IsPinned">False</x:Boolean>
                        </scg:Dictionary>
                      </sap:WorkflowViewStateService.ViewState>
                    </ui:ForEachRow>
                  </Sequence>
                </ActivityAction>
              </ui:ExcelApplicationScope.Body>
              <sap:WorkflowViewStateService.ViewState>
                <scg:Dictionary x:TypeArguments="x:String, x:Object">
                  <x:Boolean x:Key="IsExpanded">True</x:Boolean>
                  <x:Boolean x:Key="IsPinned">False</x:Boolean>
                </scg:Dictionary>
              </sap:WorkflowViewStateService.ViewState>
            </ui:ExcelApplicationScope>
          </Sequence>
          <FlowStep.Next>
            <x:Reference>__ReferenceID1</x:Reference>
          </FlowStep.Next>
        </FlowStep>
      </FlowStep.Next>
    </FlowStep>
    <x:Reference>__ReferenceID2</x:Reference>
    <FlowStep x:Name="__ReferenceID5">
      <sap:WorkflowViewStateService.ViewState>
        <scg:Dictionary x:TypeArguments="x:String, x:Object">
          <av:Point x:Key="ShapeLocation">40,97.5</av:Point>
          <av:Size x:Key="ShapeSize">200,25.3333333333333</av:Size>
          <av:PointCollection x:Key="ConnectorLocation">140,122.833333333333 140,150.333333333333 340,150.333333333333 340,30.3333333333333 370,30.3333333333333</av:PointCollection>
        </scg:Dictionary>
      </sap:WorkflowViewStateService.ViewState>
      <Delay Duration="00:00:20" sap:VirtualizedContainerService.HintSize="200,25.3333333333333" sap2010:WorkflowViewState.IdRef="Delay_1">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </Delay>
      <FlowStep.Next>
        <x:Reference>__ReferenceID3</x:Reference>
      </FlowStep.Next>
    </FlowStep>
    <x:Reference>__ReferenceID4</x:Reference>
  </Flowchart>
</Activity>