import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os

# CONFIG BLOCK - you should customize these based on your needs, as done in your UiPath Config.
in_STFilename = r"C:\Users\<USER>\Desktop\Py\Python Files\ST File 22.07.2025.xlsx"  # Set your input file path here
TransportMissesInputFilesPath = "./output"  

# Today's date
today = datetime.today()
today_str_y_md = today.strftime("%Y/%m/%d")
today_str_dmy = today.strftime("%d/%m/%Y")

# ETA date computations
ETADatePlusOne = (today + timedelta(days=1)).strftime("%d/%m/%Y")
ETADatePlusTwo = (today + timedelta(days=2)).strftime("%d/%m/%Y")
combinedStrEta = ETADatePlusOne + ETADatePlusTwo

# 1. Read Excel Data (Sheet1, fallback to Sheet12 if Sheet1 not found)
sheets_to_try = ["Sheet1", "Sheet12"]
dt_STTableInitial = None
for sheet in sheets_to_try:
    try:
        # Force Article Number and related columns to str so leading zeros are preserved!
        dt_STTableInitial = pd.read_excel(
            in_STFilename, 
            sheet_name=sheet, 
            dtype={"Article Number": str, "Product Code": str, "Connote Number": str, "ETA Date": str, "Last Article Event Subclass": str}
        )
        break
    except Exception:
        pass

if dt_STTableInitial is None:
    raise Exception("No suitable sheet found, check your input file.")

# 2. Filter rows where Article Number starts with '00' and ETA Date starts with today's date (Y/M/D)
mask_00 = dt_STTableInitial["Article Number"].str.startswith("00")
mask_eta = dt_STTableInitial["ETA Date"].astype(str).str.startswith(today_str_y_md)
dt_STTableInitial = dt_STTableInitial.loc[mask_00 & mask_eta].copy()

# 3. Raise error if no records after above filter
if dt_STTableInitial.shape[0] < 1:
    raise Exception("ST File has no records, check for holidays")

# 4. Remove rows where 'Last Article Event Subclass' CONTAINS 'Delivery_Success' OR 'Manifesting_Manifested' OR is EMPTY
def is_exclude(subclass):
    return (
        pd.isnull(subclass) or
        "Delivery_Success" in str(subclass) or
        "Manifesting_Manifested" in str(subclass)
    )
dt_STTableFinal = dt_STTableInitial.loc[~dt_STTableInitial["Last Article Event Subclass"].apply(is_exclude)].copy()

# 5. For each row: 
#    - "column 0" (Article Number) to int then str strip zeros; 
#    - "column 1" to string with '00' prefix and no single quote
dt_STTableFinal.reset_index(drop=True, inplace=True)
dt_STTableFinal["Article Number without 00"] = dt_STTableFinal["Article Number"].apply(lambda x: str(int(x)))
dt_STTableFinal["Article Number with 00"] = dt_STTableFinal["Article Number without 00"].apply(lambda x: "00" + x.replace("'", ""))

# 6. Move "Product Code" to index 1
def move_col(df, col_name, new_index):
    col = df.pop(col_name)  # remove
    df.insert(new_index, col_name, col)  # insert at new index

move_col(dt_STTableFinal, "Product Code", 1)

# 7. Keep only these columns (exact order): "Article Number", "Product Code", "Connote Number"
cols_to_keep = ["Article Number", "Product Code", "Connote Number"]
dt_AppendRange = dt_STTableFinal[cols_to_keep].copy()

# 8. Add 'Revised ETA for SAP' column (blank for now)
dt_AppendRange["Revised ETA for SAP"] = ""

# 9. Write dt_AppendRange to Excel (Sheet12, start cell A1, add headers)
month_folder = today.strftime("%B %Y")
day_folder = today.strftime("%d.%m.%Y")
out_dir = os.path.join(TransportMissesInputFilesPath, month_folder, day_folder)
os.makedirs(out_dir, exist_ok=True)
out_file = os.path.join(out_dir, f"ST File {day_folder}.xlsx")

# Write to file with openpyxl engine to preserve formatting
with pd.ExcelWriter(out_file, engine='openpyxl') as writer:
    dt_AppendRange.to_excel(writer, sheet_name="Sheet12", index=False, startrow=0)

# 10. Now update "Revised ETA for SAP" in output file depending on Product Code == 'EXP'
for i, row in dt_AppendRange.iterrows():
    if row["Product Code"] == "EXP":
        dt_AppendRange.at[i, "Revised ETA for SAP"] = ETADatePlusTwo
    else:
        dt_AppendRange.at[i, "Revised ETA for SAP"] = ETADatePlusOne

# Re-write for updated 'Revised ETA for SAP'
with pd.ExcelWriter(out_file, engine='openpyxl') as writer:
    dt_AppendRange.to_excel(writer, sheet_name="Sheet12", index=False, startrow=0)

# Write 'Article Number with 00' in cell C1
from openpyxl import load_workbook
wb = load_workbook(out_file)
ws = wb["Sheet12"]
ws["C1"] = "Article Number with 00"
wb.save(out_file)

print(f"Processed file written to: {out_file}")
