import pandas as pd
from datetime import date, timedelta
import os
import numpy as np
from openpyxl import load_workbook

# Set up the configuration dictionary with explicit file paths
in_Config = {
    "TransportMissesInputFilesPath": "C:/Users/<USER>/Desktop/Py/Python Files/Transport misses 22.07.2025.XLSX", 
    "UnsubscribedPath": "C:/Users/<USER>/Desktop/Py/Python Files/Unsubscribes.xlsx"
}

# Get current date
current_date = date.today()
month_year = current_date.strftime("%B %Y")
day_folder = current_date.strftime("%d.%m.%Y")

# Construct file paths
transport_misses_file = os.path.join(
    in_Config["TransportMissesInputFilesPath"],
    month_year,
    day_folder,
    f"Transport misses {day_folder}.XLSX"
)

st_file = os.path.join(
    in_Config["TransportMissesInputFilesPath"],
    month_year,
    day_folder,
    f"C:/Users/<USER>/Desktop/Py/Python FilesST File 22.07.2025.xlsx"
)

unsubscribed_file = in_Config["UnsubscribedPath"]

# Assign ETA dates
ETADatePlusOne = (current_date + timedelta(days=1)).strftime("%d/%m/%Y")
ETADatePlusTwo = (current_date + timedelta(days=2)).strftime("%d/%m/%Y")

# Step 1: Write headers to Sheet1
wb = load_workbook(transport_misses_file)
ws = wb["Sheet1"]
ws["P1"] = "Product Code"
ws["Q1"] = "Revised ETA"
ws["R1"] = "Unsubscribed"
wb.save(transport_misses_file)

# Step 2: Read lookup tables
dt_LookupProduct = pd.read_excel(st_file, sheet_name="Sheet12")
dt_LookupUnscribed = pd.read_excel(unsubscribed_file, sheet_name="Sheet1")

# Step 3: Read and filter Transport Misses data
dt_TargetTableProductUnscribedWhole = pd.read_excel(transport_misses_file, sheet_name="Sheet1")
dt_TargetTableProductUnscribedWhole = dt_TargetTableProductUnscribedWhole.replace("", np.nan)
dt_TargetTableProductUnscribed = dt_TargetTableProductUnscribedWhole.dropna(subset=["Purchase order number"])

# Step 4: Perform lookups and update Handling Unit
product_code_map = dt_LookupProduct.set_index("Article Number")["Product Code"].to_dict()
dt_TargetTableProductUnscribed["Product Code"] = dt_TargetTableProductUnscribed["Handling Unit"].map(product_code_map)

unsubscribed_emails = set(dt_LookupUnscribed["EMAIL ADDRESS"])
dt_TargetTableProductUnscribed["Unsubscribed"] = dt_TargetTableProductUnscribed["E-Mail Address"].apply(
    lambda x: x if x in unsubscribed_emails else np.nan
)

dt_TargetTableProductUnscribed["Handling Unit"] = dt_TargetTableProductUnscribed["Handling Unit"].apply(
    lambda x: "'" + str(int(x)) if pd.notnull(x) else x
)

# Write to Sheet5
with pd.ExcelWriter(transport_misses_file, engine="openpyxl", mode="a", if_sheet_exists="replace") as writer:
    dt_TargetTableProductUnscribed.to_excel(writer, sheet_name="Sheet5", index=False)

# Step 5: Update Revised ETA
dt_appendProductUnsubscribed = pd.read_excel(transport_misses_file, sheet_name="Sheet5")
dt_appendProductUnsubscribed["Revised ETA"] = np.where(
    dt_appendProductUnsubscribed["Product Code"] == "EXP",
    ETADatePlusOne + "-" + ETADatePlusTwo,
    ETADatePlusOne
)

# Step 6: Assign #NA to empty Unsubscribed
dt_AppendRevisedDate = dt_appendProductUnsubscribed.copy()
dt_AppendRevisedDate["Unsubscribed"] = dt_AppendRevisedDate["Unsubscribed"].fillna("#NA")

# Write to Sheet7
with pd.ExcelWriter(transport_misses_file, engine="openpyxl", mode="a", if_sheet_exists="replace") as writer:
    dt_AppendRevisedDate.to_excel(writer, sheet_name="Sheet7", index=False)

# Step 7: Filter to keep only #NA in Unsubscribed
dt_AppendRevisedDate = dt_AppendRevisedDate[dt_AppendRevisedDate["Unsubscribed"] == "#NA"]

# Write to Sheet8
with pd.ExcelWriter(transport_misses_file, engine="openpyxl", mode="a", if_sheet_exists="replace") as writer:
    dt_AppendRevisedDate.to_excel(writer, sheet_name="Sheet8", index=False)

# Step 8: Arrange columns
selected_columns = [
    "Customer", "Name 1", "Purchase order number", "Document", "Item", "Material",
    "Delivery quantity", "SU", "Delivery", "Handling Unit", "Revised ETA",
    "E-Mail Address", "#", "ID", "Deliv.Date", "Product Code", "Unsubscribed"
]
dt_renamedTable = dt_AppendRevisedDate[selected_columns].copy()

new_column_names = [
    "Customer", "Name 1", "Purchase order", "Sales Order", "Item", "Material",
    "Quantity", "Unit", "Delivery", "Handling Unit", "Revised ETA",
    "E-mail Address", "#", "ID", "Deliv.Date", "Product Code", "Unsubscribes"
]
dt_renamedTable.columns = new_column_names

# Write to RPARenamed sheet
with pd.ExcelWriter(transport_misses_file, engine="openpyxl", mode="a", if_sheet_exists="replace") as writer:
    dt_renamedTable.to_excel(writer, sheet_name="RPARenamed", index=False)