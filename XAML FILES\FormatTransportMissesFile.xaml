<Activity mc:Ignorable="sap sap2010" x:Class="FormatExcelData" xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities" xmlns:av="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities" xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation" xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation" xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib" xmlns:sd="clr-namespace:System.Data;assembly=System.Data" xmlns:ui="http://schemas.uipath.com/workflow/activities" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="in_Config" Type="InArgument(scg:Dictionary(x:String, x:Object))" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap:VirtualizedContainerService.HintSize>694.666666666667,1446.66666666667</sap:VirtualizedContainerService.HintSize>
  <sap2010:WorkflowViewState.IdRef>FormatExcelData_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <scg:List x:TypeArguments="x:String" Capacity="33">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Microsoft.VisualBasic</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>System</x:String>
      <x:String>System.Collections</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Data</x:String>
      <x:String>System.Diagnostics</x:String>
      <x:String>System.Drawing</x:String>
      <x:String>System.IO</x:String>
      <x:String>System.Linq</x:String>
      <x:String>System.Net.Mail</x:String>
      <x:String>System.Xml</x:String>
      <x:String>System.Xml.Linq</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>UiPath.Core</x:String>
      <x:String>UiPath.Core.Activities</x:String>
      <x:String>System.ComponentModel</x:String>
      <x:String>System.Runtime.Serialization</x:String>
      <x:String>System.Xml.Serialization</x:String>
      <x:String>System.Reflection</x:String>
      <x:String>System.Runtime.InteropServices</x:String>
      <x:String>UiPath.Excel.Activities</x:String>
      <x:String>UiPath.DataTableUtilities</x:String>
      <x:String>System.Collections.ObjectModel</x:String>
      <x:String>System.Activities.DynamicUpdate</x:String>
      <x:String>BalaReva.Excel.Sheets</x:String>
      <x:String>BalaReva.Excel.Base</x:String>
      <x:String>UiPath.Excel</x:String>
    </scg:List>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <scg:List x:TypeArguments="AssemblyReference" Capacity="64">
      <AssemblyReference>BalaReva.Excel</AssemblyReference>
      <AssemblyReference>BalaReva.Excel.Base</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>System.Collections.Immutable</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>System.ComponentModel.TypeConverter</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>System.Data</AssemblyReference>
      <AssemblyReference>System.Data.DataSetExtensions</AssemblyReference>
      <AssemblyReference>System.Data.Entity</AssemblyReference>
      <AssemblyReference>System.Drawing</AssemblyReference>
      <AssemblyReference>System.Linq</AssemblyReference>
      <AssemblyReference>System.Linq.Async</AssemblyReference>
      <AssemblyReference>System.Linq.Async.Queryable</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
      <AssemblyReference>System.ObjectModel</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System.ValueTuple</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>System.Xml</AssemblyReference>
      <AssemblyReference>System.Xml.Linq</AssemblyReference>
      <AssemblyReference>UiPath.Excel</AssemblyReference>
      <AssemblyReference>UiPath.Excel.Activities</AssemblyReference>
      <AssemblyReference>UiPath.Excel.Activities.Design</AssemblyReference>
      <AssemblyReference>UiPath.MicrosoftOffice365.Activities.Design</AssemblyReference>
      <AssemblyReference>UiPath.System.Activities</AssemblyReference>
      <AssemblyReference>UiPath.System.Activities.Design</AssemblyReference>
      <AssemblyReference>UiPath.UiAutomation.Activities</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
    </scg:List>
  </TextExpression.ReferencesForImplementation>
  <Flowchart sap2010:Annotation.AnnotationText="1. Rename column of sheet 1 in transport misses file&#xA;2. Apply look up to find the product code and the unsubscribed&#xA;3. Update the Revised date based on product code&#xA;4. Assign #NA to current row of Unsubscribed if empty&#xA;5. Filter data tables only with #NA values in Unsubscribed columns&#xA;6. Arrange the columns as required in RPA renamed sheet" DisplayName="FormatExcelData" sap:VirtualizedContainerService.HintSize="704.666666666667,1382" sap2010:WorkflowViewState.IdRef="Flowchart_1">
    <Flowchart.Variables>
      <Variable x:TypeArguments="sd:DataTable" Name="dt_TargetTableProductUnscribed" />
      <Variable x:TypeArguments="sd:DataTable" Name="dt_LookupProduct" />
      <Variable x:TypeArguments="sd:DataTable" Name="dt_LookupUnscribed" />
      <Variable x:TypeArguments="ui:GenericValue" Name="strProduct" />
      <Variable x:TypeArguments="ui:GenericValue" Name="strUnsubscribed" />
      <Variable x:TypeArguments="sd:DataTable" Name="dt_renamedTable" />
      <Variable x:TypeArguments="x:Int32" Default="0" Name="rowIndex" />
      <Variable x:TypeArguments="x:String" Name="ETADatePlusOne" />
      <Variable x:TypeArguments="x:String" Name="ETADatePlusTwo" />
      <Variable x:TypeArguments="sd:DataTable" Name="dt_AppendRevisedDate" />
      <Variable x:TypeArguments="sd:DataTable" Name="dt_appendProductUnsubscribed" />
    </Flowchart.Variables>
    <sap:WorkflowViewStateService.ViewState>
      <scg:Dictionary x:TypeArguments="x:String, x:Object">
        <x:Boolean x:Key="IsExpanded">True</x:Boolean>
        <av:Point x:Key="ShapeLocation">270,2.5</av:Point>
        <av:Size x:Key="ShapeSize">60,74.6666666666667</av:Size>
        <av:PointCollection x:Key="ConnectorLocation">330,39.8333333333333 386,39.8333333333333 386,89</av:PointCollection>
        <x:Double x:Key="Height">1236.6866666666667</x:Double>
        <x:Double x:Key="Width">671</x:Double>
        <x:Boolean x:Key="IsAnnotationDocked">True</x:Boolean>
      </scg:Dictionary>
    </sap:WorkflowViewStateService.ViewState>
    <Flowchart.StartNode>
      <x:Reference>__ReferenceID12</x:Reference>
    </Flowchart.StartNode>
    <FlowStep x:Name="__ReferenceID0">
      <sap:WorkflowViewStateService.ViewState>
        <scg:Dictionary x:TypeArguments="x:String, x:Object">
          <av:Point x:Key="ShapeLocation">200,593.666666666667</av:Point>
          <av:Size x:Key="ShapeSize">200,53.3333333333333</av:Size>
          <av:PointCollection x:Key="ConnectorLocation">300,647.666666666667 300,663.666666666667</av:PointCollection>
        </scg:Dictionary>
      </sap:WorkflowViewStateService.ViewState>
      <ui:ReadRange AddHeaders="True" DataTable="[dt_LookupProduct]" DisplayName="Read Range ST file data for product code" sap:VirtualizedContainerService.HintSize="200,53.3333333333333" sap2010:WorkflowViewState.IdRef="ReadRange_2" SheetName="Sheet12" WorkbookPath="[in_Config(&quot;TransportMissesInputFilesPath&quot;).ToString+&quot;\&quot;+date.today.tostring(&quot;MMMM yyyy&quot;)+&quot;\&quot;+date.Today.ToString(&quot;dd.MM.yyyy&quot;)+&quot;\&quot;+&quot;ST File &quot;+date.Today.ToString(&quot;dd.MM.yyyy&quot;) +&quot;.xlsx&quot;]">
        <ui:ReadRange.Range>
          <InArgument x:TypeArguments="x:String">
            <Literal x:TypeArguments="x:String" Value="" />
          </InArgument>
        </ui:ReadRange.Range>
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </ui:ReadRange>
      <FlowStep.Next>
        <FlowStep x:Name="__ReferenceID3">
          <sap:WorkflowViewStateService.ViewState>
            <scg:Dictionary x:TypeArguments="x:String, x:Object">
              <av:Point x:Key="ShapeLocation">200,663.666666666667</av:Point>
              <av:Size x:Key="ShapeSize">200,53.3333333333333</av:Size>
              <av:PointCollection x:Key="ConnectorLocation">300,717.666666666667 300,743.666666666667</av:PointCollection>
            </scg:Dictionary>
          </sap:WorkflowViewStateService.ViewState>
          <ui:ReadRange AddHeaders="True" DataTable="[dt_LookupUnscribed]" DisplayName="Read Range Unsubscribed file data" sap:VirtualizedContainerService.HintSize="200,53.3333333333333" sap2010:WorkflowViewState.IdRef="ReadRange_3" SheetName="Sheet1" WorkbookPath="[in_Config(&quot;UnsubscribedPath&quot;).ToString]">
            <ui:ReadRange.Range>
              <InArgument x:TypeArguments="x:String">
                <Literal x:TypeArguments="x:String" Value="" />
              </InArgument>
            </ui:ReadRange.Range>
            <sap:WorkflowViewStateService.ViewState>
              <scg:Dictionary x:TypeArguments="x:String, x:Object">
                <x:Boolean x:Key="IsExpanded">True</x:Boolean>
              </scg:Dictionary>
            </sap:WorkflowViewStateService.ViewState>
          </ui:ReadRange>
          <FlowStep.Next>
            <FlowStep x:Name="__ReferenceID4">
              <sap:WorkflowViewStateService.ViewState>
                <scg:Dictionary x:TypeArguments="x:String, x:Object">
                  <av:Point x:Key="ShapeLocation">200,743.666666666667</av:Point>
                  <av:Size x:Key="ShapeSize">200,53.3333333333333</av:Size>
                  <av:PointCollection x:Key="ConnectorLocation">300,797 300,824.02</av:PointCollection>
                </scg:Dictionary>
              </sap:WorkflowViewStateService.ViewState>
              <ui:ExcelApplicationScope Password="{x:Null}" DisplayName="Excel Application Scope read transport file data" sap:VirtualizedContainerService.HintSize="200,53.3333333333333" sap2010:WorkflowViewState.IdRef="ExcelApplicationScope_1" InstanceCachePeriod="3000" WorkbookPath="[in_Config(&quot;TransportMissesInputFilesPath&quot;).ToString+&quot;\&quot;+date.today.tostring(&quot;MMMM yyyy&quot;)+&quot;\&quot;+date.Today.ToString(&quot;dd.MM.yyyy&quot;)+&quot;\&quot;+&quot;Transport misses &quot;+date.Today.ToString(&quot;dd.MM.yyyy&quot;) +&quot;.XLSX&quot;]">
                <ui:ExcelApplicationScope.Body>
                  <ActivityAction x:TypeArguments="ui:WorkbookApplication">
                    <ActivityAction.Argument>
                      <DelegateInArgument x:TypeArguments="ui:WorkbookApplication" Name="ExcelWorkbookScope" />
                    </ActivityAction.Argument>
                    <Sequence DisplayName="Do" sap:VirtualizedContainerService.HintSize="450,83.3333333333333" sap2010:WorkflowViewState.IdRef="Sequence_3">
                      <Sequence.Variables>
                        <Variable x:TypeArguments="sd:DataTable" Name="dt_TargetTableProductUnscribedWhole" />
                      </Sequence.Variables>
                      <sap:WorkflowViewStateService.ViewState>
                        <scg:Dictionary x:TypeArguments="x:String, x:Object">
                          <x:Boolean x:Key="IsExpanded">False</x:Boolean>
                          <x:Boolean x:Key="IsPinned">False</x:Boolean>
                        </scg:Dictionary>
                      </sap:WorkflowViewStateService.ViewState>
                      <ui:ExcelReadRange AddHeaders="True" DataTable="[dt_TargetTableProductUnscribedWhole]" DisplayName="Read Range transport file data" sap:VirtualizedContainerService.HintSize="810,60" sap2010:WorkflowViewState.IdRef="ExcelReadRange_1" SheetName="Sheet1">
                        <ui:ExcelReadRange.Range>
                          <InArgument x:TypeArguments="x:String">
                            <Literal x:TypeArguments="x:String" Value="" />
                          </InArgument>
                        </ui:ExcelReadRange.Range>
                      </ui:ExcelReadRange>
                      <ui:FilterDataTable DataTable="[dt_TargetTableProductUnscribedWhole]" DisplayName="Filter Data Table to remove last rows with empty values" FilterRowsMode="Remove" sap:VirtualizedContainerService.HintSize="810,162.666666666667" sap2010:WorkflowViewState.IdRef="FilterDataTable_2" OutputDataTable="[dt_TargetTableProductUnscribed]" SelectColumnsMode="Keep">
                        <ui:FilterDataTable.Filters>
                          <scg:List x:TypeArguments="ui:FilterOperationArgument" Capacity="4">
                            <ui:FilterOperationArgument Operand="{x:Null}" BooleanOperator="And" Operator="EMPTY">
                              <ui:FilterOperationArgument.Column>
                                <InArgument x:TypeArguments="x:String">["Purchase order number"]</InArgument>
                              </ui:FilterOperationArgument.Column>
                            </ui:FilterOperationArgument>
                          </scg:List>
                        </ui:FilterDataTable.Filters>
                        <ui:FilterDataTable.SelectColumns>
                          <scg:List x:TypeArguments="InArgument" Capacity="4">
                            <x:Null />
                          </scg:List>
                        </ui:FilterDataTable.SelectColumns>
                      </ui:FilterDataTable>
                      <ui:ForEachRow ColumnNames="{x:Null}" CurrentIndex="{x:Null}" DataTable="[dt_TargetTableProductUnscribed]" DisplayName="For Each Row in Data Table to lookup product code and unsubscribed" sap:VirtualizedContainerService.HintSize="810,1235.33333333333" sap2010:WorkflowViewState.IdRef="ForEachRow_3">
                        <ui:ForEachRow.Body>
                          <ActivityAction x:TypeArguments="sd:DataRow">
                            <ActivityAction.Argument>
                              <DelegateInArgument x:TypeArguments="sd:DataRow" Name="CurrentRow" />
                            </ActivityAction.Argument>
                            <Sequence DisplayName="Body vlookup values" sap:VirtualizedContainerService.HintSize="496,1119.33333333333" sap2010:WorkflowViewState.IdRef="Sequence_4">
                              <sap:WorkflowViewStateService.ViewState>
                                <scg:Dictionary x:TypeArguments="x:String, x:Object">
                                  <x:Boolean x:Key="IsExpanded">True</x:Boolean>
                                  <x:Boolean x:Key="IsPinned">False</x:Boolean>
                                </scg:Dictionary>
                              </sap:WorkflowViewStateService.ViewState>
                              <ui:LookupDataTable LookupColumnIndex="{x:Null}" LookupDataColumn="{x:Null}" RowIndex="{x:Null}" TargetColumnIndex="{x:Null}" TargetDataColumn="{x:Null}" DataTable="[dt_LookupProduct]" DisplayName="Lookup Data Table product code" sap:VirtualizedContainerService.HintSize="434,297.333333333333" sap2010:WorkflowViewState.IdRef="LookupDataTable_8" LookupColumnName="Article Number" LookupValue="[CurrentRow(&quot;Handling Unit&quot;).ToString]" TargetColumnName="Product Code">
                                <ui:LookupDataTable.CellValue>
                                  <OutArgument x:TypeArguments="ui:GenericValue">[strProduct]</OutArgument>
                                </ui:LookupDataTable.CellValue>
                                <sap:WorkflowViewStateService.ViewState>
                                  <scg:Dictionary x:TypeArguments="x:String, x:Object">
                                    <x:Boolean x:Key="IsExpanded">True</x:Boolean>
                                    <x:Boolean x:Key="IsPinned">False</x:Boolean>
                                  </scg:Dictionary>
                                </sap:WorkflowViewStateService.ViewState>
                              </ui:LookupDataTable>
                              <Assign DisplayName="Assign lookup product code" sap:VirtualizedContainerService.HintSize="434,84.6666666666667" sap2010:WorkflowViewState.IdRef="Assign_3">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:Object">[CurrentRow("Product Code")]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:Object">[strProduct]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <ui:LookupDataTable LookupColumnIndex="{x:Null}" LookupDataColumn="{x:Null}" RowIndex="{x:Null}" TargetColumnIndex="{x:Null}" TargetDataColumn="{x:Null}" DataTable="[dt_LookupUnscribed]" DisplayName="Lookup Data Table unsubscribed" sap:VirtualizedContainerService.HintSize="434,297.333333333333" sap2010:WorkflowViewState.IdRef="LookupDataTable_9" LookupColumnName="EMAIL ADDRESS" LookupValue="[CurrentRow(&quot;E-Mail Address&quot;).ToString]" TargetColumnName="EMAIL ADDRESS">
                                <ui:LookupDataTable.CellValue>
                                  <OutArgument x:TypeArguments="ui:GenericValue">[strUnsubscribed]</OutArgument>
                                </ui:LookupDataTable.CellValue>
                                <sap:WorkflowViewStateService.ViewState>
                                  <scg:Dictionary x:TypeArguments="x:String, x:Object">
                                    <x:Boolean x:Key="IsExpanded">True</x:Boolean>
                                    <x:Boolean x:Key="IsPinned">False</x:Boolean>
                                  </scg:Dictionary>
                                </sap:WorkflowViewStateService.ViewState>
                              </ui:LookupDataTable>
                              <Assign DisplayName="Assign unsubscribed" sap:VirtualizedContainerService.HintSize="434,84.6666666666667" sap2010:WorkflowViewState.IdRef="Assign_4">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:Object">[CurrentRow("Unsubscribed")]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="ui:GenericValue">[strUnsubscribed]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign DisplayName="Assign Handling Unit to string" sap:VirtualizedContainerService.HintSize="434,84.6666666666667" sap2010:WorkflowViewState.IdRef="Assign_13">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:Object">[CurrentRow("Handling Unit")]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[("'"+Convert.ToInt64(CurrentRow("Handling Unit")).ToString)]</InArgument>
                                </Assign.Value>
                              </Assign>
                            </Sequence>
                          </ActivityAction>
                        </ui:ForEachRow.Body>
                        <sap:WorkflowViewStateService.ViewState>
                          <scg:Dictionary x:TypeArguments="x:String, x:Object">
                            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
                            <x:Boolean x:Key="IsPinned">False</x:Boolean>
                          </scg:Dictionary>
                        </sap:WorkflowViewStateService.ViewState>
                      </ui:ForEachRow>
                      <ui:ExcelWriteRange AddHeaders="True" DataTable="[dt_TargetTableProductUnscribed]" DisplayName="Write Range lookup datas" sap:VirtualizedContainerService.HintSize="810,88" sap2010:WorkflowViewState.IdRef="ExcelWriteRange_2" SheetName="Sheet5" StartingCell="A1" />
                      <ui:LogMessage DisplayName="Log Message look up datas" sap:VirtualizedContainerService.HintSize="810,94.6666666666667" sap2010:WorkflowViewState.IdRef="LogMessage_4" Level="Info" Message="[&quot;Completed writing lookup datas after vlookup&quot;]" />
                      <ui:ExcelReadRange AddHeaders="True" DataTable="[dt_appendProductUnsubscribed]" DisplayName="Read Range data with product code and unsubscribed" sap:VirtualizedContainerService.HintSize="810,60" sap2010:WorkflowViewState.IdRef="ExcelReadRange_4" SheetName="Sheet5">
                        <ui:ExcelReadRange.Range>
                          <InArgument x:TypeArguments="x:String">
                            <Literal x:TypeArguments="x:String" Value="" />
                          </InArgument>
                        </ui:ExcelReadRange.Range>
                      </ui:ExcelReadRange>
                      <ui:ForEachRow ColumnNames="{x:Null}" CurrentIndex="[rowIndex]" DataTable="[dt_appendProductUnsubscribed]" DisplayName="For Each Row in Data Table to check product code" sap:VirtualizedContainerService.HintSize="810,475.333333333333" sap2010:WorkflowViewState.IdRef="ForEachRow_5">
                        <ui:ForEachRow.Body>
                          <ActivityAction x:TypeArguments="sd:DataRow">
                            <ActivityAction.Argument>
                              <DelegateInArgument x:TypeArguments="sd:DataRow" Name="CurrentRowDates" />
                            </ActivityAction.Argument>
                            <Sequence DisplayName="Body calculating revised date" sap:VirtualizedContainerService.HintSize="776,359.333333333333" sap2010:WorkflowViewState.IdRef="Sequence_13">
                              <sap:WorkflowViewStateService.ViewState>
                                <scg:Dictionary x:TypeArguments="x:String, x:Object">
                                  <x:Boolean x:Key="IsExpanded">True</x:Boolean>
                                </scg:Dictionary>
                              </sap:WorkflowViewStateService.ViewState>
                              <If Condition="[(CurrentRowDates(&quot;Product Code&quot;).toString).Equals(&quot;EXP&quot;)]" DisplayName="If update revised eTA based on product code" sap:VirtualizedContainerService.HintSize="714,248.666666666667" sap2010:WorkflowViewState.IdRef="If_1">
                                <sap:WorkflowViewStateService.ViewState>
                                  <scg:Dictionary x:TypeArguments="x:String, x:Object">
                                    <x:Boolean x:Key="IsExpanded">True</x:Boolean>
                                    <x:Boolean x:Key="IsPinned">False</x:Boolean>
                                  </scg:Dictionary>
                                </sap:WorkflowViewStateService.ViewState>
                                <If.Then>
                                  <ui:ExcelWriteCell Cell="[&quot;Q&quot;+(rowIndex+2).ToString]" DisplayName="Write Cell revised date EXP" sap:VirtualizedContainerService.HintSize="334,88" sap2010:WorkflowViewState.IdRef="ExcelWriteCell_2" SheetName="Sheet5" Text="[ETADatePlusOne +&quot;-&quot;+ ETADatePlusTwo]" />
                                </If.Then>
                                <If.Else>
                                  <ui:ExcelWriteCell Cell="[&quot;Q&quot;+(rowIndex+2).ToString]" DisplayName="Write Cell revised date FPP" sap:VirtualizedContainerService.HintSize="334,88" sap2010:WorkflowViewState.IdRef="ExcelWriteCell_3" SheetName="Sheet5" Text="[ETADatePlusOne]" />
                                </If.Else>
                              </If>
                            </Sequence>
                          </ActivityAction>
                        </ui:ForEachRow.Body>
                        <sap:WorkflowViewStateService.ViewState>
                          <scg:Dictionary x:TypeArguments="x:String, x:Object">
                            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
                            <x:Boolean x:Key="IsPinned">False</x:Boolean>
                          </scg:Dictionary>
                        </sap:WorkflowViewStateService.ViewState>
                      </ui:ForEachRow>
                      <ui:ExcelReadRange AddHeaders="True" DataTable="[dt_AppendRevisedDate]" DisplayName="Read Range data with revised date" sap:VirtualizedContainerService.HintSize="810,60" sap2010:WorkflowViewState.IdRef="ExcelReadRange_5" SheetName="Sheet5">
                        <ui:ExcelReadRange.Range>
                          <InArgument x:TypeArguments="x:String">
                            <Literal x:TypeArguments="x:String" Value="" />
                          </InArgument>
                        </ui:ExcelReadRange.Range>
                      </ui:ExcelReadRange>
                      <ui:ForEachRow ColumnNames="{x:Null}" CurrentIndex="{x:Null}" DataTable="[dt_AppendRevisedDate]" DisplayName="For Each Row in Data Table update #NA for empty unsubscribed" sap:VirtualizedContainerService.HintSize="810,502" sap2010:WorkflowViewState.IdRef="ForEachRow_6">
                        <ui:ForEachRow.Body>
                          <ActivityAction x:TypeArguments="sd:DataRow">
                            <ActivityAction.Argument>
                              <DelegateInArgument x:TypeArguments="sd:DataRow" Name="CurrentRowUnsubscribed" />
                            </ActivityAction.Argument>
                            <Sequence DisplayName="Body" sap:VirtualizedContainerService.HintSize="546,386" sap2010:WorkflowViewState.IdRef="Sequence_16">
                              <sap:WorkflowViewStateService.ViewState>
                                <scg:Dictionary x:TypeArguments="x:String, x:Object">
                                  <x:Boolean x:Key="IsExpanded">True</x:Boolean>
                                </scg:Dictionary>
                              </sap:WorkflowViewStateService.ViewState>
                              <If Condition="[string.IsNullOrEmpty(CurrentRowUnsubscribed(&quot;Unsubscribed&quot;).ToString)]" DisplayName="If unsubscribed is empty" sap:VirtualizedContainerService.HintSize="484,275.333333333333" sap2010:WorkflowViewState.IdRef="If_2">
                                <sap:WorkflowViewStateService.ViewState>
                                  <scg:Dictionary x:TypeArguments="x:String, x:Object">
                                    <x:Boolean x:Key="IsExpanded">True</x:Boolean>
                                    <x:Boolean x:Key="IsPinned">False</x:Boolean>
                                  </scg:Dictionary>
                                </sap:WorkflowViewStateService.ViewState>
                                <If.Then>
                                  <Assign DisplayName="Assign #NA" sap:VirtualizedContainerService.HintSize="434,84.6666666666667" sap2010:WorkflowViewState.IdRef="Assign_12">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:Object">[CurrentRowUnsubscribed("Unsubscribed")]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:Object">["#NA"]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                </If.Then>
                              </If>
                            </Sequence>
                          </ActivityAction>
                        </ui:ForEachRow.Body>
                      </ui:ForEachRow>
                      <ui:ExcelWriteRange AddHeaders="True" DataTable="[dt_AppendRevisedDate]" DisplayName="Write Range data with revised dates and #NA" sap:VirtualizedContainerService.HintSize="810,88" sap2010:WorkflowViewState.IdRef="ExcelWriteRange_3" SheetName="Sheet7" StartingCell="A1" />
                      <ui:FilterDataTable DataTable="[dt_AppendRevisedDate]" DisplayName="Filter Data Table to keep #NA unsubscribed" FilterRowsMode="Keep" sap:VirtualizedContainerService.HintSize="810,162.666666666667" sap2010:WorkflowViewState.IdRef="FilterDataTable_1" OutputDataTable="[dt_AppendRevisedDate]" SelectColumnsMode="Keep">
                        <ui:FilterDataTable.Filters>
                          <scg:List x:TypeArguments="ui:FilterOperationArgument" Capacity="4">
                            <ui:FilterOperationArgument BooleanOperator="And" Operator="CONTAINS">
                              <ui:FilterOperationArgument.Column>
                                <InArgument x:TypeArguments="x:String">["Unsubscribed"]</InArgument>
                              </ui:FilterOperationArgument.Column>
                              <ui:FilterOperationArgument.Operand>
                                <InArgument x:TypeArguments="x:String">["#NA"]</InArgument>
                              </ui:FilterOperationArgument.Operand>
                            </ui:FilterOperationArgument>
                          </scg:List>
                        </ui:FilterDataTable.Filters>
                        <ui:FilterDataTable.SelectColumns>
                          <scg:List x:TypeArguments="InArgument" Capacity="4">
                            <x:Null />
                          </scg:List>
                        </ui:FilterDataTable.SelectColumns>
                      </ui:FilterDataTable>
                      <ui:LogMessage DisplayName="Log Message Filtered table with #NA" sap:VirtualizedContainerService.HintSize="810,94.6666666666667" sap2010:WorkflowViewState.IdRef="LogMessage_5" Level="Info" Message="[&quot;Filtered datatble with #NA completed&quot;]" />
                      <ui:ExcelWriteRange AddHeaders="True" DataTable="[dt_AppendRevisedDate]" DisplayName="Write Range table after keeping only #NA" sap:VirtualizedContainerService.HintSize="810,88" sap2010:WorkflowViewState.IdRef="ExcelWriteRange_1" SheetName="Sheet8" StartingCell="A1" />
                    </Sequence>
                  </ActivityAction>
                </ui:ExcelApplicationScope.Body>
                <sap:WorkflowViewStateService.ViewState>
                  <scg:Dictionary x:TypeArguments="x:String, x:Object">
                    <x:Boolean x:Key="IsExpanded">True</x:Boolean>
                  </scg:Dictionary>
                </sap:WorkflowViewStateService.ViewState>
              </ui:ExcelApplicationScope>
              <FlowStep.Next>
                <FlowStep x:Name="__ReferenceID5">
                  <sap:WorkflowViewStateService.ViewState>
                    <scg:Dictionary x:TypeArguments="x:String, x:Object">
                      <av:Point x:Key="ShapeLocation">200,824.02</av:Point>
                      <av:Size x:Key="ShapeSize">200,53.3333333333333</av:Size>
                      <av:PointCollection x:Key="ConnectorLocation">300,877.353333333333 300,907.353333333333 295.895,907.353333333333 295.895,910</av:PointCollection>
                    </scg:Dictionary>
                  </sap:WorkflowViewStateService.ViewState>
                  <ui:BuildDataTable DataTable="[dt_renamedTable]" DisplayName="Build Data Table to arrange columns" sap:VirtualizedContainerService.HintSize="200,53.3333333333333" sap2010:WorkflowViewState.IdRef="BuildDataTable_1" TableInfo="&lt;NewDataSet&gt;&#xA;  &lt;xs:schema id=&quot;NewDataSet&quot; xmlns=&quot;&quot; xmlns:xs=&quot;http://www.w3.org/2001/XMLSchema&quot; xmlns:msdata=&quot;urn:schemas-microsoft-com:xml-msdata&quot;&gt;&#xA;    &lt;xs:element name=&quot;NewDataSet&quot; msdata:IsDataSet=&quot;true&quot; msdata:MainDataTable=&quot;TableName&quot; msdata:UseCurrentLocale=&quot;true&quot;&gt;&#xA;      &lt;xs:complexType&gt;&#xA;        &lt;xs:choice minOccurs=&quot;0&quot; maxOccurs=&quot;unbounded&quot;&gt;&#xA;          &lt;xs:element name=&quot;TableName&quot;&gt;&#xA;            &lt;xs:complexType&gt;&#xA;              &lt;xs:sequence&gt;&#xA;                &lt;xs:element name=&quot;Customer&quot; msdata:Caption=&quot;&quot; minOccurs=&quot;0&quot;&gt;&#xA;                  &lt;xs:simpleType&gt;&#xA;                    &lt;xs:restriction base=&quot;xs:string&quot;&gt;&#xA;                      &lt;xs:maxLength value=&quot;100&quot; /&gt;&#xA;                    &lt;/xs:restriction&gt;&#xA;                  &lt;/xs:simpleType&gt;&#xA;                &lt;/xs:element&gt;&#xA;                &lt;xs:element name=&quot;Name_x0020_1&quot; msdata:Caption=&quot;&quot; minOccurs=&quot;0&quot;&gt;&#xA;                  &lt;xs:simpleType&gt;&#xA;                    &lt;xs:restriction base=&quot;xs:string&quot;&gt;&#xA;                      &lt;xs:maxLength value=&quot;100&quot; /&gt;&#xA;                    &lt;/xs:restriction&gt;&#xA;                  &lt;/xs:simpleType&gt;&#xA;                &lt;/xs:element&gt;&#xA;                &lt;xs:element name=&quot;Purchase_x0020_order&quot; msdata:Caption=&quot;&quot; type=&quot;xs:string&quot; minOccurs=&quot;0&quot; /&gt;&#xA;                &lt;xs:element name=&quot;Sales_x0020_Order&quot; msdata:Caption=&quot;&quot; minOccurs=&quot;0&quot;&gt;&#xA;                  &lt;xs:simpleType&gt;&#xA;                    &lt;xs:restriction base=&quot;xs:string&quot;&gt;&#xA;                      &lt;xs:maxLength value=&quot;100&quot; /&gt;&#xA;                    &lt;/xs:restriction&gt;&#xA;                  &lt;/xs:simpleType&gt;&#xA;                &lt;/xs:element&gt;&#xA;                &lt;xs:element name=&quot;Item&quot; msdata:Caption=&quot;&quot; minOccurs=&quot;0&quot;&gt;&#xA;                  &lt;xs:simpleType&gt;&#xA;                    &lt;xs:restriction base=&quot;xs:string&quot;&gt;&#xA;                      &lt;xs:maxLength value=&quot;100&quot; /&gt;&#xA;                    &lt;/xs:restriction&gt;&#xA;                  &lt;/xs:simpleType&gt;&#xA;                &lt;/xs:element&gt;&#xA;                &lt;xs:element name=&quot;Material&quot; msdata:Caption=&quot;&quot; minOccurs=&quot;0&quot;&gt;&#xA;                  &lt;xs:simpleType&gt;&#xA;                    &lt;xs:restriction base=&quot;xs:string&quot;&gt;&#xA;                      &lt;xs:maxLength value=&quot;100&quot; /&gt;&#xA;                    &lt;/xs:restriction&gt;&#xA;                  &lt;/xs:simpleType&gt;&#xA;                &lt;/xs:element&gt;&#xA;                &lt;xs:element name=&quot;Quantity&quot; msdata:Caption=&quot;&quot; minOccurs=&quot;0&quot;&gt;&#xA;                  &lt;xs:simpleType&gt;&#xA;                    &lt;xs:restriction base=&quot;xs:string&quot;&gt;&#xA;                      &lt;xs:maxLength value=&quot;100&quot; /&gt;&#xA;                    &lt;/xs:restriction&gt;&#xA;                  &lt;/xs:simpleType&gt;&#xA;                &lt;/xs:element&gt;&#xA;                &lt;xs:element name=&quot;Unit&quot; msdata:Caption=&quot;&quot; minOccurs=&quot;0&quot;&gt;&#xA;                  &lt;xs:simpleType&gt;&#xA;                    &lt;xs:restriction base=&quot;xs:string&quot;&gt;&#xA;                      &lt;xs:maxLength value=&quot;100&quot; /&gt;&#xA;                    &lt;/xs:restriction&gt;&#xA;                  &lt;/xs:simpleType&gt;&#xA;                &lt;/xs:element&gt;&#xA;                &lt;xs:element name=&quot;Delivery&quot; msdata:Caption=&quot;&quot; minOccurs=&quot;0&quot;&gt;&#xA;                  &lt;xs:simpleType&gt;&#xA;                    &lt;xs:restriction base=&quot;xs:string&quot;&gt;&#xA;                      &lt;xs:maxLength value=&quot;100&quot; /&gt;&#xA;                    &lt;/xs:restriction&gt;&#xA;                  &lt;/xs:simpleType&gt;&#xA;                &lt;/xs:element&gt;&#xA;                &lt;xs:element name=&quot;Handling_x0020_Unit&quot; msdata:Caption=&quot;&quot; minOccurs=&quot;0&quot;&gt;&#xA;                  &lt;xs:simpleType&gt;&#xA;                    &lt;xs:restriction base=&quot;xs:string&quot;&gt;&#xA;                      &lt;xs:maxLength value=&quot;100&quot; /&gt;&#xA;                    &lt;/xs:restriction&gt;&#xA;                  &lt;/xs:simpleType&gt;&#xA;                &lt;/xs:element&gt;&#xA;                &lt;xs:element name=&quot;Revised_x0020_ETA&quot; msdata:Caption=&quot;&quot; minOccurs=&quot;0&quot;&gt;&#xA;                  &lt;xs:simpleType&gt;&#xA;                    &lt;xs:restriction base=&quot;xs:string&quot;&gt;&#xA;                      &lt;xs:maxLength value=&quot;24&quot; /&gt;&#xA;                    &lt;/xs:restriction&gt;&#xA;                  &lt;/xs:simpleType&gt;&#xA;                &lt;/xs:element&gt;&#xA;                &lt;xs:element name=&quot;E-mail_x0020_Address&quot; msdata:Caption=&quot;&quot; minOccurs=&quot;0&quot;&gt;&#xA;                  &lt;xs:simpleType&gt;&#xA;                    &lt;xs:restriction base=&quot;xs:string&quot;&gt;&#xA;                      &lt;xs:maxLength value=&quot;100&quot; /&gt;&#xA;                    &lt;/xs:restriction&gt;&#xA;                  &lt;/xs:simpleType&gt;&#xA;                &lt;/xs:element&gt;&#xA;                &lt;xs:element name=&quot;_x0023_&quot; msdata:Caption=&quot;&quot; minOccurs=&quot;0&quot;&gt;&#xA;                  &lt;xs:simpleType&gt;&#xA;                    &lt;xs:restriction base=&quot;xs:string&quot;&gt;&#xA;                      &lt;xs:maxLength value=&quot;100&quot; /&gt;&#xA;                    &lt;/xs:restriction&gt;&#xA;                  &lt;/xs:simpleType&gt;&#xA;                &lt;/xs:element&gt;&#xA;                &lt;xs:element name=&quot;ID&quot; msdata:Caption=&quot;&quot; minOccurs=&quot;0&quot;&gt;&#xA;                  &lt;xs:simpleType&gt;&#xA;                    &lt;xs:restriction base=&quot;xs:string&quot;&gt;&#xA;                      &lt;xs:maxLength value=&quot;100&quot; /&gt;&#xA;                    &lt;/xs:restriction&gt;&#xA;                  &lt;/xs:simpleType&gt;&#xA;                &lt;/xs:element&gt;&#xA;                &lt;xs:element name=&quot;Deliv.Date&quot; msdata:Caption=&quot;&quot; minOccurs=&quot;0&quot;&gt;&#xA;                  &lt;xs:simpleType&gt;&#xA;                    &lt;xs:restriction base=&quot;xs:string&quot;&gt;&#xA;                      &lt;xs:maxLength value=&quot;100&quot; /&gt;&#xA;                    &lt;/xs:restriction&gt;&#xA;                  &lt;/xs:simpleType&gt;&#xA;                &lt;/xs:element&gt;&#xA;                &lt;xs:element name=&quot;Product_x0020_Code&quot; msdata:Caption=&quot;&quot; minOccurs=&quot;0&quot;&gt;&#xA;                  &lt;xs:simpleType&gt;&#xA;                    &lt;xs:restriction base=&quot;xs:string&quot;&gt;&#xA;                      &lt;xs:maxLength value=&quot;100&quot; /&gt;&#xA;                    &lt;/xs:restriction&gt;&#xA;                  &lt;/xs:simpleType&gt;&#xA;                &lt;/xs:element&gt;&#xA;                &lt;xs:element name=&quot;Unsubscribes&quot; msdata:Caption=&quot;&quot; minOccurs=&quot;0&quot;&gt;&#xA;                  &lt;xs:simpleType&gt;&#xA;                    &lt;xs:restriction base=&quot;xs:string&quot;&gt;&#xA;                      &lt;xs:maxLength value=&quot;100&quot; /&gt;&#xA;                    &lt;/xs:restriction&gt;&#xA;                  &lt;/xs:simpleType&gt;&#xA;                &lt;/xs:element&gt;&#xA;              &lt;/xs:sequence&gt;&#xA;            &lt;/xs:complexType&gt;&#xA;          &lt;/xs:element&gt;&#xA;        &lt;/xs:choice&gt;&#xA;      &lt;/xs:complexType&gt;&#xA;    &lt;/xs:element&gt;&#xA;  &lt;/xs:schema&gt;&#xA;&lt;/NewDataSet&gt;">
                    <sap:WorkflowViewStateService.ViewState>
                      <scg:Dictionary x:TypeArguments="x:String, x:Object">
                        <x:Boolean x:Key="IsExpanded">True</x:Boolean>
                      </scg:Dictionary>
                    </sap:WorkflowViewStateService.ViewState>
                  </ui:BuildDataTable>
                  <FlowStep.Next>
                    <FlowStep x:Name="__ReferenceID6">
                      <sap:WorkflowViewStateService.ViewState>
                        <scg:Dictionary x:TypeArguments="x:String, x:Object">
                          <av:Point x:Key="ShapeLocation">78.895,910</av:Point>
                          <av:Size x:Key="ShapeSize">434,85.3333333333333</av:Size>
                          <av:PointCollection x:Key="ConnectorLocation">295.895,995.333333333333 295.895,1025.33333333333 300,1025.33333333333 300,1033.66666666667</av:PointCollection>
                        </scg:Dictionary>
                      </sap:WorkflowViewStateService.ViewState>
                      <Assign DisplayName="Assign arrange column headers and values" sap:VirtualizedContainerService.HintSize="434,85.3333333333333" sap2010:WorkflowViewState.IdRef="Assign_5">
                        <Assign.To>
                          <OutArgument x:TypeArguments="sd:DataTable">[dt_renamedTable]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="sd:DataTable" xml:space="preserve">[(From p In dt_AppendRevisedDate.AsEnumerable()
Group By
col1 = p("Name 1").ToString,
col2 = p("E-Mail Address").ToString,
col3 = p("Handling Unit").ToString,
col4 = p("Delivery").ToString,
col5 = p("Material").ToString,
col6 = p("Delivery quantity").ToString,
col7 = p("SU").ToString,
col8 = p("Purchase order number").ToString,
col9 = p("Document").ToString,
col10 = p("Item").ToString,
col11 = p("Customer").ToString,
col12 = p("Person").ToString,
col13 = p("#").ToString,
col14 = p("ID").ToString,
col15 = p("Deliv.Date").ToString,
col16 = p("Product Code").ToString,
col17 = p("Revised ETA").ToString,
col18 = p("Unsubscribed").ToString
Into Group
Select dt_renamedTable.Rows.Add({col11,col1,col8,col9,col10,col5,col6,col7,col4,col3,col17,col2,col13,col14,col15,col16,col18})).CopyToDatatable]</InArgument>
                        </Assign.Value>
                        <sap:WorkflowViewStateService.ViewState>
                          <scg:Dictionary x:TypeArguments="x:String, x:Object">
                            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
                          </scg:Dictionary>
                        </sap:WorkflowViewStateService.ViewState>
                      </Assign>
                      <FlowStep.Next>
                        <FlowStep x:Name="__ReferenceID7">
                          <sap:WorkflowViewStateService.ViewState>
                            <scg:Dictionary x:TypeArguments="x:String, x:Object">
                              <av:Point x:Key="ShapeLocation">200,1033.66666666667</av:Point>
                              <av:Size x:Key="ShapeSize">200,53.3333333333333</av:Size>
                              <av:PointCollection x:Key="ConnectorLocation">300,1087 300,1104.02</av:PointCollection>
                            </scg:Dictionary>
                          </sap:WorkflowViewStateService.ViewState>
                          <ui:WriteRange AddHeaders="True" DataTable="[dt_renamedTable]" DisplayName="Write Range arranged data" sap:VirtualizedContainerService.HintSize="200,53.3333333333333" sap2010:WorkflowViewState.IdRef="WriteRange_1" SheetName="RPARenamed" StartingCell="A1" WorkbookPath="[in_Config(&quot;TransportMissesInputFilesPath&quot;).ToString+&quot;\&quot;+date.today.tostring(&quot;MMMM yyyy&quot;)+&quot;\&quot;+date.Today.ToString(&quot;dd.MM.yyyy&quot;)+&quot;\&quot;+&quot;Transport misses &quot;+date.Today.ToString(&quot;dd.MM.yyyy&quot;) +&quot;.XLSX&quot;]">
                            <sap:WorkflowViewStateService.ViewState>
                              <scg:Dictionary x:TypeArguments="x:String, x:Object">
                                <x:Boolean x:Key="IsExpanded">True</x:Boolean>
                              </scg:Dictionary>
                            </sap:WorkflowViewStateService.ViewState>
                          </ui:WriteRange>
                          <FlowStep.Next>
                            <FlowStep x:Name="__ReferenceID11">
                              <sap:WorkflowViewStateService.ViewState>
                                <scg:Dictionary x:TypeArguments="x:String, x:Object">
                                  <av:Point x:Key="ShapeLocation">200,1104.02</av:Point>
                                  <av:Size x:Key="ShapeSize">200,53.3333333333333</av:Size>
                                </scg:Dictionary>
                              </sap:WorkflowViewStateService.ViewState>
                              <ui:LogMessage DisplayName="Log Message completed columns arranged" sap:VirtualizedContainerService.HintSize="200,53.3333333333333" sap2010:WorkflowViewState.IdRef="LogMessage_3" Level="Info" Message="[&quot;arrange columns arranged in RPA renamed sheet&quot;]">
                                <sap:WorkflowViewStateService.ViewState>
                                  <scg:Dictionary x:TypeArguments="x:String, x:Object">
                                    <x:Boolean x:Key="IsExpanded">True</x:Boolean>
                                  </scg:Dictionary>
                                </sap:WorkflowViewStateService.ViewState>
                              </ui:LogMessage>
                            </FlowStep>
                          </FlowStep.Next>
                        </FlowStep>
                      </FlowStep.Next>
                    </FlowStep>
                  </FlowStep.Next>
                </FlowStep>
              </FlowStep.Next>
            </FlowStep>
          </FlowStep.Next>
        </FlowStep>
      </FlowStep.Next>
    </FlowStep>
    <FlowStep x:Name="__ReferenceID8">
      <sap:WorkflowViewStateService.ViewState>
        <scg:Dictionary x:TypeArguments="x:String, x:Object">
          <av:Point x:Key="ShapeLocation">160,294.02</av:Point>
          <av:Size x:Key="ShapeSize">200,53.3333333333333</av:Size>
          <av:PointCollection x:Key="ConnectorLocation">260,347.353333333333 300,347.353333333333 300,364.02</av:PointCollection>
        </scg:Dictionary>
      </sap:WorkflowViewStateService.ViewState>
      <ui:WriteCell Cell="P1" DisplayName="Write Cell Product code" sap:VirtualizedContainerService.HintSize="200,53.3333333333333" sap2010:WorkflowViewState.IdRef="WriteCell_7" SheetName="Sheet1" Text="Product Code" WorkbookPath="[in_Config(&quot;TransportMissesInputFilesPath&quot;).ToString+&quot;\&quot;+date.today.tostring(&quot;MMMM yyyy&quot;)+&quot;\&quot;+date.Today.ToString(&quot;dd.MM.yyyy&quot;)+&quot;\&quot;+&quot;Transport misses &quot;+date.Today.ToString(&quot;dd.MM.yyyy&quot;) +&quot;.XLSX&quot;]">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </ui:WriteCell>
      <FlowStep.Next>
        <FlowStep x:Name="__ReferenceID1">
          <sap:WorkflowViewStateService.ViewState>
            <scg:Dictionary x:TypeArguments="x:String, x:Object">
              <av:Point x:Key="ShapeLocation">200,364.02</av:Point>
              <av:Size x:Key="ShapeSize">200,53.3333333333333</av:Size>
              <av:PointCollection x:Key="ConnectorLocation">300,418.02 300,446.686666666667</av:PointCollection>
            </scg:Dictionary>
          </sap:WorkflowViewStateService.ViewState>
          <ui:WriteCell Cell="Q1" DisplayName="Write Cell Revised eta" sap:VirtualizedContainerService.HintSize="200,53.3333333333333" sap2010:WorkflowViewState.IdRef="WriteCell_8" SheetName="Sheet1" Text="Revised ETA" WorkbookPath="[in_Config(&quot;TransportMissesInputFilesPath&quot;).ToString+&quot;\&quot;+date.today.tostring(&quot;MMMM yyyy&quot;)+&quot;\&quot;+date.Today.ToString(&quot;dd.MM.yyyy&quot;)+&quot;\&quot;+&quot;Transport misses &quot;+date.Today.ToString(&quot;dd.MM.yyyy&quot;) +&quot;.XLSX&quot;]">
            <sap:WorkflowViewStateService.ViewState>
              <scg:Dictionary x:TypeArguments="x:String, x:Object">
                <x:Boolean x:Key="IsExpanded">True</x:Boolean>
              </scg:Dictionary>
            </sap:WorkflowViewStateService.ViewState>
          </ui:WriteCell>
          <FlowStep.Next>
            <FlowStep x:Name="__ReferenceID2">
              <sap:WorkflowViewStateService.ViewState>
                <scg:Dictionary x:TypeArguments="x:String, x:Object">
                  <av:Point x:Key="ShapeLocation">200,446.686666666667</av:Point>
                  <av:Size x:Key="ShapeSize">200,53.3333333333333</av:Size>
                  <av:PointCollection x:Key="ConnectorLocation">300,500.686666666667 300,524.02</av:PointCollection>
                </scg:Dictionary>
              </sap:WorkflowViewStateService.ViewState>
              <ui:WriteCell Cell="R1" DisplayName="Write Cell header unsubscribed" sap:VirtualizedContainerService.HintSize="200,53.3333333333333" sap2010:WorkflowViewState.IdRef="WriteCell_9" SheetName="Sheet1" Text="Unsubscribed" WorkbookPath="[in_Config(&quot;TransportMissesInputFilesPath&quot;).ToString+&quot;\&quot;+date.today.tostring(&quot;MMMM yyyy&quot;)+&quot;\&quot;+date.Today.ToString(&quot;dd.MM.yyyy&quot;)+&quot;\&quot;+&quot;Transport misses &quot;+date.Today.ToString(&quot;dd.MM.yyyy&quot;) +&quot;.XLSX&quot;]">
                <sap:WorkflowViewStateService.ViewState>
                  <scg:Dictionary x:TypeArguments="x:String, x:Object">
                    <x:Boolean x:Key="IsExpanded">True</x:Boolean>
                  </scg:Dictionary>
                </sap:WorkflowViewStateService.ViewState>
              </ui:WriteCell>
              <FlowStep.Next>
                <FlowStep x:Name="__ReferenceID10">
                  <sap:WorkflowViewStateService.ViewState>
                    <scg:Dictionary x:TypeArguments="x:String, x:Object">
                      <av:Point x:Key="ShapeLocation">200,524.02</av:Point>
                      <av:Size x:Key="ShapeSize">200,53.3333333333333</av:Size>
                      <av:PointCollection x:Key="ConnectorLocation">300,578.02 300,593.666666666667</av:PointCollection>
                    </scg:Dictionary>
                  </sap:WorkflowViewStateService.ViewState>
                  <ui:LogMessage DisplayName="Log Message start reading datatbles for vlookups" sap:VirtualizedContainerService.HintSize="200,53.3333333333333" sap2010:WorkflowViewState.IdRef="LogMessage_2" Level="Info" Message="[&quot;start reading datatbles for vlookups&quot;]">
                    <sap:WorkflowViewStateService.ViewState>
                      <scg:Dictionary x:TypeArguments="x:String, x:Object">
                        <x:Boolean x:Key="IsExpanded">True</x:Boolean>
                      </scg:Dictionary>
                    </sap:WorkflowViewStateService.ViewState>
                  </ui:LogMessage>
                  <FlowStep.Next>
                    <x:Reference>__ReferenceID0</x:Reference>
                  </FlowStep.Next>
                </FlowStep>
              </FlowStep.Next>
            </FlowStep>
          </FlowStep.Next>
        </FlowStep>
      </FlowStep.Next>
    </FlowStep>
    <x:Reference>__ReferenceID1</x:Reference>
    <x:Reference>__ReferenceID2</x:Reference>
    <x:Reference>__ReferenceID3</x:Reference>
    <x:Reference>__ReferenceID4</x:Reference>
    <x:Reference>__ReferenceID5</x:Reference>
    <x:Reference>__ReferenceID6</x:Reference>
    <x:Reference>__ReferenceID7</x:Reference>
    <FlowStep x:Name="__ReferenceID12">
      <sap:WorkflowViewStateService.ViewState>
        <scg:Dictionary x:TypeArguments="x:String, x:Object">
          <av:Point x:Key="ShapeLocation">169,89</av:Point>
          <av:Size x:Key="ShapeSize">434,84.6666666666667</av:Size>
          <av:PointCollection x:Key="ConnectorLocation">386,173.666666666667 386,189</av:PointCollection>
        </scg:Dictionary>
      </sap:WorkflowViewStateService.ViewState>
      <Assign DisplayName="Assign EtaDateplus one" sap:VirtualizedContainerService.HintSize="434,84.6666666666667" sap2010:WorkflowViewState.IdRef="Assign_10">
        <Assign.To>
          <OutArgument x:TypeArguments="x:String">[ETADatePlusOne]</OutArgument>
        </Assign.To>
        <Assign.Value>
          <InArgument x:TypeArguments="x:String">[date.Today.AddDays(1).ToString("dd/MM/yyyy")]</InArgument>
        </Assign.Value>
      </Assign>
      <FlowStep.Next>
        <FlowStep x:Name="__ReferenceID9">
          <sap:WorkflowViewStateService.ViewState>
            <scg:Dictionary x:TypeArguments="x:String, x:Object">
              <av:Point x:Key="ShapeLocation">169,189</av:Point>
              <av:Size x:Key="ShapeSize">434,84.6666666666667</av:Size>
              <av:PointCollection x:Key="ConnectorLocation">386,273.666666666667 386,320.686666666667 360,320.686666666667</av:PointCollection>
            </scg:Dictionary>
          </sap:WorkflowViewStateService.ViewState>
          <Assign DisplayName="Assign ETADateplustwo" sap:VirtualizedContainerService.HintSize="434,84.6666666666667" sap2010:WorkflowViewState.IdRef="Assign_11">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[ETADatePlusTwo]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[date.Today.AddDays(2).ToString("dd/MM/yyyy")]</InArgument>
            </Assign.Value>
          </Assign>
          <FlowStep.Next>
            <x:Reference>__ReferenceID8</x:Reference>
          </FlowStep.Next>
        </FlowStep>
      </FlowStep.Next>
    </FlowStep>
    <x:Reference>__ReferenceID9</x:Reference>
    <x:Reference>__ReferenceID10</x:Reference>
    <x:Reference>__ReferenceID11</x:Reference>
  </Flowchart>
</Activity>