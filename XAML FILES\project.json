{"name": "XAML FILES", "projectId": "49431152-82b0-4dbc-83ff-c75685c47c6c", "description": "Blank Process", "main": "FormatTransportMissesFile.xaml", "dependencies": {"UiPath.System.Activities": "[25.4.4]"}, "webServices": [], "entitiesStores": [], "schemaVersion": "4.0", "studioVersion": "**********", "projectVersion": "1.0.0", "runtimeOptions": {"autoDispose": false, "netFrameworkLazyLoading": false, "isPausable": true, "isAttended": false, "requiresUserInteraction": true, "supportsPersistence": false, "workflowSerialization": "NewtonsoftJson", "excludedLoggedData": ["Private:*", "*password*"], "executionType": "Workflow", "readyForPiP": false, "startsInPiP": false, "mustRestoreAllDependencies": true, "pipType": "ChildSession"}, "designOptions": {"projectProfile": "Developement", "outputType": "Process", "libraryOptions": {"includeOriginalXaml": false, "privateWorkflows": []}, "processOptions": {"ignoredFiles": []}, "fileInfoCollection": [], "saveToCloud": false}, "expressionLanguage": "VisualBasic", "entryPoints": [{"filePath": "FormatTransportMissesFile.xaml", "uniqueId": "b2e5c649-f2d0-4d70-a058-167076570ad9", "input": [], "output": []}], "isTemplate": false, "templateProjectData": {}, "publishData": {}, "targetFramework": "Windows"}