{"ActivityBindings": {"UiPath.Activities.System.Agentic.RunAgent": [{"Type": "agent", "PublishNullValues": false, "Key": {"Value": "BindingsKey", "ValueSource": "Property"}, "Values": {"name": {"Value": "ProcessName", "ValueSource": "Property"}, "folderPath": {"Value": "FolderPath", "ValueSource": "Property"}}, "Arguments": {"BindingsVersion": {"Value": "2.1", "ValueSource": "Constant"}}, "SubBindings": [], "DefaultValueSource": "Property"}], "UiPath.Activities.System.Jobs.RunJob": [{"Type": "process", "PublishNullValues": false, "Key": {"Value": "BindingsKey", "ValueSource": "Property"}, "Values": {"folderPath": {"Value": "FolderPath", "ValueSource": "Property"}, "name": {"Value": "ProcessName", "ValueSource": "Property"}}, "Arguments": {"BindingsVersion": {"Value": "2.2", "ValueSource": "Constant"}}, "SubBindings": [], "DefaultValueSource": "Property"}], "UiPath.Core.Activities.AddAndProcessQueueItem": [{"Type": "queue", "Values": {"name": {"Value": "QueueType", "ValueSource": "Property"}, "folderPath": {"Value": "FolderPath", "ValueSource": "Property"}}, "DefaultValueSource": "<PERSON><PERSON><PERSON>"}], "UiPath.Core.Activities.AddQueueItem": [{"Type": "queue", "PublishNullValues": false, "Key": {"Value": "BindingsKey", "ValueSource": "Property"}, "Values": {"folderPath": {"Value": "FolderPath", "ValueSource": "Property"}, "name": {"Value": "QueueType", "ValueSource": "Property"}}, "Arguments": {"BindingsVersion": {"Value": "2.2", "ValueSource": "Constant"}}, "SubBindings": [], "DefaultValueSource": "Property"}], "UiPath.Core.Activities.AddTransactionItem": [{"Type": "queue", "PublishNullValues": false, "Key": {"Value": "BindingsKey", "ValueSource": "Property"}, "Values": {"folderPath": {"Value": "FolderPath", "ValueSource": "Property"}, "name": {"Value": "QueueType", "ValueSource": "Property"}}, "Arguments": {"BindingsVersion": {"Value": "2.2", "ValueSource": "Constant"}}, "SubBindings": [], "DefaultValueSource": "Property"}], "UiPath.Core.Activities.BeginProcess": [{"Type": "process", "Values": {"name": {"Value": "ProcessName", "ValueSource": "Property"}, "folderPath": {"Value": "FolderPath", "ValueSource": "Property"}}, "DefaultValueSource": "<PERSON><PERSON><PERSON>"}], "UiPath.Core.Activities.BulkAddQueueItems": [{"Type": "queue", "PublishNullValues": false, "Key": {"Value": "BindingsKey", "ValueSource": "Property"}, "Values": {"folderPath": {"Value": "FolderPath", "ValueSource": "Property"}, "name": {"Value": "QueueName", "ValueSource": "Property"}}, "Arguments": {"BindingsVersion": {"Value": "2.2", "ValueSource": "Constant"}}, "SubBindings": [], "DefaultValueSource": "Property"}], "UiPath.Core.Activities.DeleteQueueItems": [{"Type": "queue", "Values": {"folderPath": {"Value": "FolderPath", "ValueSource": "Property"}}, "DefaultValueSource": "<PERSON><PERSON><PERSON>"}], "UiPath.Core.Activities.EvaluateBusinessRule": [{"Type": "businessRule", "PublishNullValues": false, "Key": {"Value": "BindingsKey", "ValueSource": "Property"}, "Values": {"folderPath": {"Value": "FolderPath", "ValueSource": "Property"}, "name": {"Value": "BusinessRule", "ValueSource": "Property"}}, "Arguments": {"BindingsVersion": {"Value": "2.2", "ValueSource": "Constant"}}, "SubBindings": [], "DefaultValueSource": "Property"}], "UiPath.Core.Activities.GetJobs": [{"Type": "process", "Values": {"folderPath": {"Value": "FolderPath", "ValueSource": "Property"}}, "DefaultValueSource": "<PERSON><PERSON><PERSON>"}], "UiPath.Core.Activities.GetQueueItem": [{"Type": "queue", "PublishNullValues": false, "Key": {"Value": "BindingsKey", "ValueSource": "Property"}, "Values": {"folderPath": {"Value": "FolderPath", "ValueSource": "Property"}, "name": {"Value": "QueueType", "ValueSource": "Property"}}, "Arguments": {"BindingsVersion": {"Value": "2.2", "ValueSource": "Constant"}}, "SubBindings": [], "DefaultValueSource": "Property"}], "UiPath.Core.Activities.GetQueueItems": [{"Type": "queue", "PublishNullValues": false, "Key": {"Value": "BindingsKey", "ValueSource": "Property"}, "Values": {"folderPath": {"Value": "FolderPath", "ValueSource": "Property"}, "name": {"Value": "QueueName", "ValueSource": "Property"}}, "Arguments": {"BindingsVersion": {"Value": "2.2", "ValueSource": "Constant"}}, "SubBindings": [], "DefaultValueSource": "Property"}], "UiPath.Core.Activities.GetRobotAsset": [{"Type": "asset", "PublishNullValues": false, "Key": {"Value": "BindingsKey", "ValueSource": "Property"}, "Values": {"folderPath": {"Value": "FolderPath", "ValueSource": "Property"}, "name": {"Value": "AssetName", "ValueSource": "Property"}}, "Arguments": {"BindingsVersion": {"Value": "2.2", "ValueSource": "Constant"}}, "SubBindings": [], "DefaultValueSource": "Property"}], "UiPath.Core.Activities.GetRobotCredential": [{"Type": "asset", "PublishNullValues": false, "Key": {"Value": "BindingsKey", "ValueSource": "Property"}, "Values": {"folderPath": {"Value": "FolderPath", "ValueSource": "Property"}, "name": {"Value": "AssetName", "ValueSource": "Property"}}, "Arguments": {"BindingsVersion": {"Value": "2.2", "ValueSource": "Constant"}}, "SubBindings": [], "DefaultValueSource": "Property"}], "UiPath.Core.Activities.GetTransactionItem": [{"Type": "queue", "Values": {"name": {"Value": "QueueType", "ValueSource": "Property"}, "folderPath": {"Value": "FolderPath", "ValueSource": "Property"}}, "DefaultValueSource": "<PERSON><PERSON><PERSON>"}], "UiPath.Core.Activities.InvokeProcess": [{"Type": "process", "PublishNullValues": false, "Key": {"Value": "BindingsKey", "ValueSource": "Property"}, "Values": {"folderPath": {"Value": "FolderPath", "ValueSource": "Property"}, "name": {"Value": "ProcessName", "ValueSource": "Property"}}, "Arguments": {"BindingsVersion": {"Value": "2.2", "ValueSource": "Constant"}}, "SubBindings": [], "DefaultValueSource": "Property"}], "UiPath.Core.Activities.PostponeTransactionItem": [{"Type": "queue", "Values": {"folderPath": {"Value": "FolderPath", "ValueSource": "Property"}}, "DefaultValueSource": "<PERSON><PERSON><PERSON>"}], "UiPath.Core.Activities.QueueTrigger": [{"Type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PublishNullValues": false, "Key": {"Value": "BindingsKey", "ValueSource": "Property"}, "Values": {"folderPath": {"Value": "FolderPath", "ValueSource": "Property"}, "name": {"Value": "QueueName", "ValueSource": "Property"}}, "Arguments": {"ItemsActivationThreshold": {"Value": "ItemsActivationThreshold", "ValueSource": "Property"}, "ItemsPerJobActivationTarget": {"Value": "ItemsPerJobActivationTarget", "ValueSource": "Property"}, "MaxJobsForActivation": {"Value": "MaxJobsForActivation", "ValueSource": "Property"}, "BindingsVersion": {"Value": "2.1", "ValueSource": "Constant"}}, "SubBindings": [], "DefaultValueSource": "Property"}], "UiPath.Core.Activities.SetAsset": [{"Type": "asset", "PublishNullValues": false, "Key": {"Value": "BindingsKey", "ValueSource": "Property"}, "Values": {"folderPath": {"Value": "FolderPath", "ValueSource": "Property"}, "name": {"Value": "AssetName", "ValueSource": "Property"}}, "Arguments": {"BindingsVersion": {"Value": "2.2", "ValueSource": "Constant"}}, "SubBindings": [], "DefaultValueSource": "Property"}], "UiPath.Core.Activities.SetCredential": [{"Type": "asset", "PublishNullValues": false, "Key": {"Value": "BindingsKey", "ValueSource": "Property"}, "Values": {"folderPath": {"Value": "FolderPath", "ValueSource": "Property"}, "name": {"Value": "CredentialName", "ValueSource": "Property"}}, "Arguments": {"BindingsVersion": {"Value": "2.2", "ValueSource": "Constant"}}, "SubBindings": [], "DefaultValueSource": "Property"}], "UiPath.Core.Activities.SetTransactionProgress": [{"Type": "queue", "Values": {"folderPath": {"Value": "FolderPath", "ValueSource": "Property"}}, "DefaultValueSource": "<PERSON><PERSON><PERSON>"}], "UiPath.Core.Activities.SetTransactionStatus": [{"Type": "queue", "Values": {"folderPath": {"Value": "FolderPath", "ValueSource": "Property"}}, "DefaultValueSource": "<PERSON><PERSON><PERSON>"}], "UiPath.Core.Activities.StartJob": [{"Type": "process", "PublishNullValues": false, "Key": {"Value": "BindingsKey", "ValueSource": "Property"}, "Values": {"folderPath": {"Value": "FolderPath", "ValueSource": "Property"}, "name": {"Value": "ProcessName", "ValueSource": "Property"}}, "Arguments": {"BindingsVersion": {"Value": "2.2", "ValueSource": "Constant"}}, "SubBindings": [], "DefaultValueSource": "Property"}], "UiPath.Core.Activities.StopJob": [{"Type": "process", "Values": {"folderPath": {"Value": "FolderPath", "ValueSource": "Property"}}, "DefaultValueSource": "<PERSON><PERSON><PERSON>"}], "UiPath.Core.Activities.Storage.DeleteStorageFile": [{"Type": "bucket", "PublishNullValues": false, "Key": {"Value": "BindingsKey", "ValueSource": "Property"}, "Values": {"folderPath": {"Value": "FolderPath", "ValueSource": "Property"}, "name": {"Value": "StorageBucketName", "ValueSource": "Property"}}, "Arguments": {"BindingsVersion": {"Value": "2.2", "ValueSource": "Constant"}}, "SubBindings": [], "DefaultValueSource": "Property"}], "UiPath.Core.Activities.Storage.DownloadStorageFile": [{"Type": "bucket", "PublishNullValues": false, "Key": {"Value": "BindingsKey", "ValueSource": "Property"}, "Values": {"folderPath": {"Value": "FolderPath", "ValueSource": "Property"}, "name": {"Value": "StorageBucketName", "ValueSource": "Property"}}, "Arguments": {"BindingsVersion": {"Value": "2.2", "ValueSource": "Constant"}}, "SubBindings": [], "DefaultValueSource": "Property"}], "UiPath.Core.Activities.Storage.ListStorageFiles": [{"Type": "bucket", "PublishNullValues": false, "Key": {"Value": "BindingsKey", "ValueSource": "Property"}, "Values": {"folderPath": {"Value": "FolderPath", "ValueSource": "Property"}, "name": {"Value": "StorageBucketName", "ValueSource": "Property"}}, "Arguments": {"BindingsVersion": {"Value": "2.2", "ValueSource": "Constant"}}, "SubBindings": [], "DefaultValueSource": "Property"}], "UiPath.Core.Activities.Storage.ReadStorageText": [{"Type": "bucket", "PublishNullValues": false, "Key": {"Value": "BindingsKey", "ValueSource": "Property"}, "Values": {"folderPath": {"Value": "FolderPath", "ValueSource": "Property"}, "name": {"Value": "StorageBucketName", "ValueSource": "Property"}}, "Arguments": {"BindingsVersion": {"Value": "2.2", "ValueSource": "Constant"}}, "SubBindings": [], "DefaultValueSource": "Property"}], "UiPath.Core.Activities.Storage.UploadStorageFile": [{"Type": "bucket", "PublishNullValues": false, "Key": {"Value": "BindingsKey", "ValueSource": "Property"}, "Values": {"folderPath": {"Value": "FolderPath", "ValueSource": "Property"}, "name": {"Value": "StorageBucketName", "ValueSource": "Property"}}, "Arguments": {"BindingsVersion": {"Value": "2.2", "ValueSource": "Constant"}}, "SubBindings": [], "DefaultValueSource": "Property"}], "UiPath.Core.Activities.Storage.WriteStorageText": [{"Type": "bucket", "PublishNullValues": false, "Key": {"Value": "BindingsKey", "ValueSource": "Property"}, "Values": {"folderPath": {"Value": "FolderPath", "ValueSource": "Property"}, "name": {"Value": "StorageBucketName", "ValueSource": "Property"}}, "Arguments": {"BindingsVersion": {"Value": "2.2", "ValueSource": "Constant"}}, "SubBindings": [], "DefaultValueSource": "Property"}], "UiPath.Core.Activities.TimeTrigger": [{"Type": "TimeTrigger", "PublishNullValues": false, "Key": {"Value": "N/A", "ValueSource": "Property"}, "Values": {}, "Arguments": {"TimeZone": {"Value": "TimeZone", "ValueSource": "Property"}, "CronExpression": {"Value": "CronExpression", "ValueSource": "Property"}, "BindingsVersion": {"Value": "2.1", "ValueSource": "Constant"}}, "SubBindings": [], "DefaultValueSource": "Property"}], "UiPath.Core.Activities.WaitQueueItem": [{"Type": "queue", "PublishNullValues": false, "Key": {"Value": "BindingsKey", "ValueSource": "Property"}, "Values": {"folderPath": {"Value": "FolderPath", "ValueSource": "Property"}, "name": {"Value": "QueueName", "ValueSource": "Property"}}, "Arguments": {"BindingsVersion": {"Value": "2.2", "ValueSource": "Constant"}}, "SubBindings": [], "DefaultValueSource": "Property"}]}}